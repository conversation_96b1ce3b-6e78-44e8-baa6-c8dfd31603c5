# 🐱 公职猫 (GongZhiMall)

> 全国首款面向体制内个人的AI总助：U盘级私密，秘书般懂你

[![项目状态](https://img.shields.io/badge/状态-开发中-green.svg)](https://gitee.com/yh_ian/gongzhimall)
[![技术栈](https://img.shields.io/badge/技术栈-React%20Native%20%2B%20Electron-blue.svg)](#技术栈)
[![许可证](https://img.shields.io/badge/许可证-私有-red.svg)](#许可证)

## 📋 项目概述

公职猫是全国首款专为体制内个人设计的AI总助，致力于成为中青年干部的智能搭档。产品核心理念是"U盘级私密，秘书般懂你"，弥补体制内软件服务于组织而不服务于个人的行业空白。

### 🎯 核心价值主张

- **U盘级私密**：
  - 数据100%属于用户本地存储
  - 数据可方便跨端传输，支持随岗调动
- **秘书般懂你**：
  - 深度理解体制内工作语境和流程
  - 智能识别主办/协办/参与角色
  - 提供差异化的任务管理和提醒

### 🎯 目标用户

- **主要用户**：中青年干部（1000万潜在用户）
- **核心痛点**：没有秘书和下属，集管理与执行于一身
- **使用场景**：日常工作管理、文档处理、任务跟踪、知识查询

## 🚀 最新进展

### ✅ 已完成功能
- **OCR文字识别**：iOS Vision框架 + Android MLKit集成完成
- **AI分析管道**：四层AI架构（规则引擎→本地AI→云端AI→专业知识库）
- **移动端UI**：完整的移动端界面设计和实现
- **本地数据存储**：SQLite + AES-256加密
- **证件照处理**：离线人像抠图、背景替换、标准尺寸导出（Task #41）

### 🔄 进行中
- **角色识别优化**：基于开源库的智能角色判断
- **语音识别集成**：与AI分析管道的深度整合
- **跨平台数据同步**：移动端与桌面端数据同步机制

### 📋 下一步计划
- **种子用户测试**：5-10名体制内用户测试验证
- **MVP集成测试**：核心功能端到端测试
- **投资人演示准备**：产品演示材料制作

## 🏗️ 项目架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    公职猫 AI 总助                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│   📱 移动端      │   🖥️ 桌面端      │   🌐 后端管理            │
│  React Native   │   Electron      │   Node.js + Express    │
├─────────────────┼─────────────────┼─────────────────────────┤
│              🧠 四层AI架构                                    │
│  规则引擎 → 本地AI → 云端AI → 专业知识库                      │
├─────────────────────────────────────────────────────────────┤
│              🗄️ 本地数据存储                                  │
│         SQLite + AES-256加密 + 跨端同步                      │
└─────────────────────────────────────────────────────────────┘
```

### 技术选型原则
- **成本最优**：规则引擎为主，AI为辅
- **隐私优先**：本地存储，可选云端
- **渐进增强**：MVP → 本地AI → 云端AI → 专业知识库

## 🚀 快速开始

### 环境要求
```bash
Node.js >= 18.0.0
yarn >= 1.22.0
React Native CLI >= 12.0.0
Electron >= 28.0.0
```

### 一键安装
```bash
# 克隆项目
git clone https://gitee.com/yh_ian/gongzhimall.git
cd gongzhimall

# 安装所有依赖
yarn install

# 移动端依赖
cd mobile && yarn install && cd ..

# 桌面端依赖
cd desktop && yarn install && cd ..

# 后端依赖
cd backend && yarn install && cd ..
```

### 启动开发环境

#### 🖥️ 桌面端开发
```bash
cd desktop
yarn dev
# 访问: Electron应用自动启动
```

#### 📱 移动端开发
```bash
cd mobile
yarn start
# 然后按 'a' 启动Android，或按 'i' 启动iOS
```

#### 🌐 后端开发
```bash
cd backend
yarn dev
# 访问: http://localhost:3000
```

#### 🎛️ 管理后台
```bash
cd admin-web
yarn start
# 访问: http://localhost:3002
```

## 📁 项目结构

```
gongzhimall/
├── 📱 mobile/                      # React Native移动端
│   ├── src/components/             # 共享组件
│   ├── src/services/              # 业务服务
│   └── src/styles/                # 样式文件
├── 🖥️ desktop/                     # Electron桌面端
│   ├── src/renderer/              # 渲染进程
│   ├── src/main/                  # 主进程
│   └── src/db/                    # 数据库层
├── 🌐 backend/                     # Node.js后端API
│   ├── controllers/               # 控制器
│   ├── models/                    # 数据模型
│   ├── routes/                    # 路由定义
│   └── middleware/                # 中间件
├── 🎛️ admin-web/                   # React管理后台
│   ├── src/                       # 前端源码
│   └── public/                    # 静态资源
├── 🔧 shared/                      # 共享代码
├── 📋 docs/                        # 项目文档
│   ├── 01_strategy/               # 战略规划
│   ├── 02_requirements/           # 需求文档
│   ├── 03_design/                 # 设计文档
│   ├── 04_architecture/           # 架构文档
│   └── 05_guides/                 # 开发指南
├── 🎨 界面设计效果示意/             # UI设计文档
├── 📊 .taskmaster/                 # TaskMaster项目管理
├── ⚙️ .cursor/                     # Cursor IDE配置
└── 🔒 scripts/                     # 项目脚本
```

## 🛠️ 开发工具与流程

### TaskMaster项目管理
```bash
# 查看任务列表
npx task-master list

# 查看下一个任务
npx task-master next

# 查看任务详情
npx task-master show <task-id>

# 更新任务状态
npx task-master set-status --id=<task-id> --status=done
```

### Git工作流程
```bash
# 1. 创建功能分支
git checkout -b feature/your-feature-name

# 2. 开发并提交
git add .
git commit -m "feat: 功能描述"

# 3. 推送分支
git push origin feature/your-feature-name

# 4. 创建Pull Request
# 访问Gitee创建PR，需要至少1个审查者
```

### 代码规范
- **提交格式**: 遵循Conventional Commits
- **分支命名**: `feature/`, `bugfix/`, `hotfix/`
- **代码审查**: 所有PR需要审查通过
- **安全检查**: Git hooks自动检查敏感信息

## 🔒 权限管理

### 团队角色
- **核心开发者**: 完整访问权限
- **平台开发者**: 特定平台代码访问
- **外包团队**: 受限访问，禁止敏感代码
- **设计师**: 设计文档和UI代码访问

### 分支保护
- **master**: 需要2个审查者，禁止直接推送
- **develop**: 需要1个审查者，禁止直接推送
- **feature**: 允许推送，合并需审查

详细权限配置请参考：[Git权限管理指南](docs/05_guides/git-permission-setup.md)

## 🧠 AI架构设计

### 四层AI架构
```
┌─────────────────────────────────────────────────────────────┐
│ 第四层：专业知识库 (云端)                                      │
│ • 行业知识问答 • 政策查询 • 文档模板                           │
├─────────────────────────────────────────────────────────────┤
│ 第三层：云端AI (可选)                                         │
│ • OpenRouter API • 复杂推理 • 高级功能                       │
├─────────────────────────────────────────────────────────────┤
│ 第二层：本地AI (轻量级)                                       │
│ • 文本理解 • 意图识别 • 个性化学习                            │
├─────────────────────────────────────────────────────────────┤
│ 第一层：规则引擎 (兜底)                                       │
│ • 关键词匹配 • 模式识别 • 基础分类                            │
└─────────────────────────────────────────────────────────────┘
```

### 成本控制策略
- **80%功能**：规则引擎实现，零运营成本
- **用户引导**：系统推荐 + 用户确认 + 学习记忆
- **渐进升级**：MVP规则引擎 → V2本地AI → V3云端AI

## 🔐 安全与隐私

### 数据安全
- **本地存储**: 100%本地数据，支持离线使用
- **数据库加密**: AES-256级别加密
- **密钥管理**: Electron safeStorage API
- **传输安全**: HTTPS + 证书验证

### 隐私保护
- **个人数据**: 100%本地存储，不上传云端
- **匿名统计**: 仅收集设备类型、地区分布等群体画像
- **用户控制**: 完全控制数据收集和上报策略

### 安全特性
- **离线优先**: 核心功能支持完全离线
- **数据导出**: 支持数据备份和迁移
- **访问控制**: 多层权限管理
- **审计日志**: 完整的操作记录

## 📱 移动端开发

### 技术栈
- **框架**: React Native 0.73.0
- **语言**: TypeScript
- **状态管理**: Redux Toolkit
- **导航**: React Navigation 6
- **数据库**: SQLite + react-native-sqlite-storage

### 开发命令
```bash
cd mobile

# 启动开发服务器
yarn start

# Android开发
yarn android

# iOS开发
yarn ios

# 运行测试
yarn test

# 代码检查
yarn lint
```

### 发布配置
- **Android**: 华为应用市场
- **iOS**: App Store上架

## 🖥️ 桌面端开发

### 技术栈
- **框架**: Electron 28+
- **前端**: React + TypeScript
- **状态管理**: Redux Toolkit
- **数据库**: better-sqlite3-multiple-ciphers
- **UI组件**: Ant Design

### 开发命令
```bash
cd desktop

# 开发模式
yarn dev

# 生产构建
yarn build

# 打包应用
yarn dist

# 运行测试
yarn test
```

### 支持平台
- **Windows**: Windows 10/11
- **macOS**: macOS 10.15+
- **Linux**: Ubuntu 20.04+, 统信UOS

## 🎛️ 管理后台开发

### 技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Create React App
- **UI组件**: 待集成 (推荐Ant Design)
- **状态管理**: 待集成 (推荐Redux Toolkit)

### 核心功能
- **系统概览**: 设备统计、使用情况、系统状态监控
- **设备管理**: 设备注册、状态监控、远程配置
- **用户统计**: 匿名用户行为分析、使用趋势
- **系统监控**: API健康状态、数据库状态、AI服务状态
- **配置管理**: 应用全局配置、功能开关、版本管理

### 开发命令
```bash
cd admin-web

# 开发模式 (端口3002)
yarn start

# 生产构建
yarn build

# 运行测试
yarn test
```

### 数据来源
- **后端API**: 通过`/api/admin`接口获取运营数据
- **匿名统计**: 基于`user_uuid`的聚合分析数据
- **系统监控**: 实时健康检查和性能指标

## 🌐 后端开发

### 技术栈
- **框架**: Node.js + Express
- **数据库**: SQLite (本地) + 云端数据库 (运营数据)
- **认证**: JWT + bcrypt
- **安全**: helmet + cors + morgan
- **日志**: morgan + 自定义日志

### API服务架构
```
┌─────────────────────────────────────────────────────────────┐
│                    后端API服务                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│   🔐 认证服务    │   📄 文档服务    │   👥 用户管理            │
│  /api/auth      │  /api/documents │   /api/users            │
├─────────────────┼─────────────────┼─────────────────────────┤
│              🎛️ 运营管理服务                                 │
│         /api/admin (系统监控、数据分析、配置管理)              │
├─────────────────────────────────────────────────────────────┤
│              🗄️ 混合数据存储                                  │
│    本地SQLite (用户数据) + 云端数据库 (运营数据)               │
└─────────────────────────────────────────────────────────────┘
```

### 核心API服务
- **认证服务** (`/api/auth`): 匿名设备认证、订阅验证、Token管理
- **文档服务** (`/api/documents`): 文档处理、云端模板、知识库查询
- **用户管理** (`/api/users`): 匿名偏好同步、设备配置管理
- **运营管理** (`/api/admin`): 系统监控、匿名数据统计、配置管理

### 用户身份与设备管理

**重要说明**：公职猫的"用户"概念与传统应用完全不同，严格遵循"U盘级私密"原则：

#### 🔐 匿名身份设计
- **无传统注册**: 不需要手机号、邮箱等个人信息注册
- **客户端生成**: 每个设备首次启动时生成唯一的`user_uuid`
- **匿名关联**: 云端仅通过`user_uuid`提供服务，无法反向识别真实用户
- **设备独立**: 每台设备都是独立的"用户"，不主动关联设备关系

#### 🔄 跨设备数据同步
- **用户主导**: 用户可选择将`user_uuid`和加密数据手动同步到新设备
- **二维码传输**: 通过加密二维码实现设备间数据传输
- **本地解密**: 所有数据在本地解密，云端不参与解密过程
- **随时断开**: 用户可随时停止设备间的关联关系

#### 🛡️ 隐私保护设计
遵循"绝对隐私优先 (Privacy by Design)"原则：
- **零PII存储**: 云端严禁存储任何个人可识别信息
- **匿名关联**: 通过客户端生成的`user_uuid`进行匿名关联
- **职责分离**: 云端仅负责增值服务、匿名分析、应用配置
- **数据最小化**: API传输遵循最小化原则

### 开发命令
```bash
cd backend

# 开发模式 (端口3001)
yarn dev

# 生产启动
yarn start

# 运行测试
yarn test
```

## 📊 项目管理

### 里程碑规划
- **第1周**: UI/UX基础和核心输入功能
- **第2周**: 智能识别和AI处理能力
- **第3周**: 任务管理和差异化功能
- **第4周**: 跨端同步和功能完善

### 迭代发布
- **周迭代**: 每周发布一个可用版本
- **功能驱动**: 以用户价值为导向
- **快速反馈**: 及时收集用户反馈并调整

详细流程请参考：[周迭代工作流程](docs/05_guides/weekly-iteration-workflow.md)

## 🧪 测试策略

### 测试类型
- **单元测试**: Jest + React Native Testing Library
- **集成测试**: Supertest (后端API)
- **E2E测试**: Detox (移动端) + Playwright (桌面端)
- **性能测试**: 数据库加密性能 + UI响应性能

### 测试命令
```bash
# 运行所有测试
yarn test

# 移动端测试
cd mobile && yarn test

# 桌面端测试
cd desktop && yarn test

# 后端测试
cd backend && yarn test

# E2E测试
yarn test:e2e
```

## 🚀 部署配置

### 环境配置
- **开发环境**: 本地开发，热重载
- **测试环境**: 自动化测试，CI/CD
- **预生产环境**: 生产环境镜像
- **生产环境**: 正式发布版本

### 部署方式
- **桌面端**: 官网下载 (www.gongzhimall.com)
- **移动端**: 华为应用市场 (Android) + App Store (iOS)
- **后端API**: 云服务器部署，提供认证、文档、运营管理服务
- **管理后台**: 内网部署，运营团队使用

## 📞 联系方式

- **项目邮箱**: <EMAIL>
- **技术支持**: [Gitee Issues](https://gitee.com/yh_ian/gongzhimall/issues)
- **项目主页**: www.gongzhimall.com

## 🤝 贡献指南

1. **Fork项目** (内部团队)
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

详细贡献指南请参考：[CONTRIBUTING.md](CONTRIBUTING.md)

## 📄 许可证

**私有项目** - 本项目为私有项目，不对外开源。所有代码和相关资源仅供内部使用，未经授权不得复制、分发或用于其他用途。

## 🏆 团队介绍

公职猫团队具备腾讯、阿里、大华等大厂经验和垂直toG领域10年经验，致力于为体制内用户提供最贴心的AI助理服务。

---

<div align="center">

**🐱 公职猫 - 让AI成为您最懂您的工作伙伴**

*公职猫团队 © 2025*

</div> 