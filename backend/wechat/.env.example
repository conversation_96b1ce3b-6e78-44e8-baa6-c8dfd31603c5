# 公职猫微信转发服务环境变量配置
# 这是一个示例文件，请根据实际情况修改

# ==================== 基础配置 ====================
NODE_ENV=production
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com

# ==================== 数据库配置 ====================
MYSQL_HOST=**********
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=your_secure_password_here
MYSQL_DATABASE=gongzhimall_wechat

# ==================== 企业微信配置 ====================
WECHAT_CORP_ID=your_corp_id_here
WECHAT_CORP_SECRET=your_corp_secret_here
WECHAT_AGENT_ID=1000002
WECHAT_TOKEN=your_token_here
WECHAT_ENCODING_AES_KEY=your_aes_key_here

# ==================== 极光推送配置 ====================
JPUSH_APP_KEY=your_jpush_app_key_here
JPUSH_MASTER_SECRET=your_jpush_master_secret_here

# ==================== 安全配置 ====================
JWT_SECRET=your_jwt_secret_at_least_32_characters_long
FILE_ENCRYPTION_KEY=your_file_encryption_key_32_chars
API_SECRET_KEY=your_api_secret_key_32_characters

# ==================== 文件存储配置 ====================
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600
FILE_CLEANUP_INTERVAL=3600000
FILE_MAX_AGE_DAYS=3

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/wechat-service
ENABLE_DB_LOGGING=true
