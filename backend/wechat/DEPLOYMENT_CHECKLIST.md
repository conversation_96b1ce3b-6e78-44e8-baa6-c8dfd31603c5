# 公职猫微信转发服务 - 部署就绪检查清单

## 🎉 重构完成概述

✅ **架构迁移完成**：从腾讯云函数(SCF)成功迁移到腾讯云轻量应用服务器
✅ **文件临时缓存机制**：实现"阅后即焚"文件管理，用户下载后立即删除
✅ **安全加密存储**：所有文件使用AES-256-CBC加密存储
✅ **JWT下载令牌**：24小时有效期，过期自动重新生成
✅ **智能推送通知**：支持不同文件类型的个性化通知
✅ **自动化运维**：定时清理、健康检查、监控统计
✅ **代码质量保证**：所有新代码语法检查通过，核心功能测试通过

---

## 📋 部署前检查清单

### 1. 服务器环境准备

- [X] 腾讯云服务器已就绪
- [X] 宝塔面板已安装并配置
- [X] 域名 `wechat.api.gongzhimall.com` A记录已指向服务器公网IP
- [X] SSL证书已配置
- [X] 确认Node.js 18+已安装（在宝塔面板-软件商店中安装）
- [ ] 确认MySQL服务运行正常
- [ ] 创建文件存储目录：`mkdir -p /data/wechat-cache`
- [ ] 设置目录权限：`chmod 755 /data/wechat-cache`
  > 注意：文件存储目录放在/data下而非/www下，避免通过Web直接访问，提高安全性

### 2. 数据库配置

- [ ] 腾讯云MySQL实例已创建
- [ ] 数据库用户权限已配置
- [ ] 执行数据库初始化脚本：`mysql < data/database.sql`
- [ ] 验证表结构创建成功
- [ ] 配置数据库连接池参数

### 3. 环境变量配置

复制并配置以下环境变量到服务器的 `.env`文件：

```bash
# 必需配置
NODE_ENV=production
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com

# 数据库配置
MYSQL_HOST=**********
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=wechat@gongzhimall123
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信配置
WECHAT_CORP_ID=ww857dc7bfe97b085b
WECHAT_CORP_SECRET=eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I
WECHAT_AGENT_ID=1000002
WECHAT_TOKEN=gongzhimallEnterprise2025
WECHAT_ENCODING_AES_KEY=zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv

# 极光推送配置
JPUSH_APP_KEY=bd2c958b83dc679759a25664
JPUSH_MASTER_SECRET=59a9af913b1d2dd85f4cbc24

# 文件服务配置
FILE_STORAGE_PATH=/data/wechat-cache
FILE_ENCRYPTION_KEY=gongzhimall-file-encryption-2025
MAX_FILE_SIZE=104857600
DOWNLOAD_TOKEN_EXPIRES_HOURS=24

# 安全配置
TOKEN_SECRET=gongzhimall-app-2025
WECHAT_BINDING_TOKEN_SECRET=gongzhimall_binding_secret_2025
```

### 4. 宝塔面板部署步骤

#### 4.1 上传代码
- [ ] 将整个 `backend/wechat/` 目录上传到服务器 `/www/wwwroot/wechat.api.gongzhimall.com/`
- [ ] 在宝塔面板-文件管理中设置目录权限为755

#### 4.2 Node.js项目配置
- [ ] 在宝塔面板-软件商店安装Node.js管理器
- [ ] 添加Node.js项目：
  - 项目域名：`wechat.api.gongzhimall.com`
  - 项目目录：`/www/wwwroot/wechat.api.gongzhimall.com`
  - 启动文件：`index.js`
  - 端口：3000
- [ ] 配置SSL证书（已完成）

#### 4.3 依赖安装
```bash
# 进入项目目录
cd /www/wwwroot/wechat.api.gongzhimall.com

# 安装生产依赖
npm install --production

# 或使用yarn
yarn install --production
```

### 5. 服务启动验证

#### 5.1 语法检查
```bash
# 进入项目目录
cd /www/wwwroot/wechat.api.gongzhimall.com

# 语法检查
node -c index.js
node -c service/fileService.js
node -c service/service.js
node -c data/database.js

# 核心功能测试
node scripts/test-core-functions.js
```

#### 5.2 宝塔面板启动服务
- [ ] 在宝塔面板-Node.js项目管理中启动项目
- [ ] 检查项目状态为"运行中"
- [ ] 查看项目日志确认启动成功

#### 5.3 备用启动方式（命令行）
```bash
# 直接启动
npm start

# 或使用PM2（推荐生产环境）
pm2 start index.js --name gongzhimall-wechat
pm2 save
pm2 startup
```

### 6. API端点验证

#### 6.1 基础端点测试
```bash
# 健康检查
curl https://wechat.api.gongzhimall.com/health

# 预期返回：{"status":"ok","timestamp":"...","version":"..."}
```

#### 6.2 完整API验证
- [ ] 健康检查：`GET https://wechat.api.gongzhimall.com/health`
- [ ] 企业微信Webhook：`POST https://wechat.api.gongzhimall.com/api/wechat/callback`
- [ ] 消息同步：`GET https://wechat.api.gongzhimall.com/api/sync/messages`
- [ ] 文件下载：`GET https://wechat.api.gongzhimall.com/api/media/download/:token`

#### 6.3 宝塔面板监控
- [ ] 在宝塔面板-Node.js项目中查看项目运行状态
- [ ] 检查项目日志无错误信息
- [ ] 监控服务器资源使用情况

### 7. 监控和日志

#### 7.1 日志配置
- [ ] 日志目录创建：`mkdir -p /var/log/wechat-service`
- [ ] 日志轮转配置

#### 7.2 宝塔面板定时任务配置
- [ ] 在宝塔面板-计划任务中添加：
  
**文件清理任务**：
- 任务类型：Shell脚本
- 任务名称：清理微信转发过期文件
- 执行周期：每小时
- 脚本内容：
  ```bash
  cd /www/wwwroot/wechat.api.gongzhimall.com && node scripts/cleanup-files.js
  ```

**数据库清理任务**：
- 任务类型：Shell脚本
- 任务名称：清理微信转发数据库
- 执行周期：每天 02:00
- 脚本内容：
  ```bash
  cd /www/wwwroot/wechat.api.gongzhimall.com && node scripts/cleanup-database.js
  ```

#### 7.3 备用定时任务配置（crontab）
```bash
# 每小时清理过期文件
0 * * * * cd /www/wwwroot/wechat.api.gongzhimall.com && node scripts/cleanup-files.js

# 每天凌晨2点清理数据库
0 2 * * * cd /www/wwwroot/wechat.api.gongzhimall.com && node scripts/cleanup-database.js
```

### 8. 安全配置

#### 8.1 宝塔面板安全设置
- [x] SSL证书已配置
- [ ] 在宝塔面板-安全中开放3000端口
- [ ] 配置防火墙规则，限制3000端口仅允许本机访问
- [ ] 配置Nginx反向代理（宝塔面板会自动配置）

#### 8.2 应用安全验证
- [ ] 请求限制配置验证
- [ ] CORS域名配置验证
- [ ] JWT令牌安全性检查
- [ ] 文件加密功能验证

---

## 🚀 部署命令参考

### Docker部署

```bash
# 构建镜像
docker build -t gongzhimall-wechat .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### PM2部署

```bash
# 启动服务
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs gongzhimall-wechat
```

---

## 🔧 故障排除

### 常见问题

1. **文件权限错误**：确保 `/var/www/cache`目录权限为755
2. **数据库连接失败**：检查MySQL服务状态和网络连接
3. **推送失败**：验证极光推送密钥配置
4. **文件下载404**：检查文件是否已过期或被清理

### 日志查看

```bash
# 应用日志
tail -f /var/log/wechat-service/app.log

# PM2日志
pm2 logs gongzhimall-wechat

# Docker日志
docker-compose logs -f app
```

---

## 📊 性能监控

### 关键指标

- **文件缓存使用量**：通过 `/health`端点监控
- **下载令牌生成速率**：监控JWT令牌生成频率
- **文件清理效率**：监控定时清理任务执行情况
- **推送成功率**：监控极光推送成功率

### 告警配置

- 文件缓存使用超过80%
- 数据库连接池耗尽
- 推送失败率超过5%
- 文件下载错误率超过1%

---

## ✅ 部署完成确认

部署完成后，请确认以下功能正常：

1. **企业微信消息接收**：发送测试消息，确认Webhook正常
2. **文件下载功能**：发送图片/语音消息，确认下载链接生效
3. **推送通知功能**：确认移动端收到推送通知
4. **定时清理功能**：确认过期文件和数据自动清理
5. **健康检查**：确认 `/health`端点返回正常状态

**🎯 部署成功标志**：移动端用户可以正常接收企业微信消息，下载文件，且文件在下载后或3天后自动删除。
