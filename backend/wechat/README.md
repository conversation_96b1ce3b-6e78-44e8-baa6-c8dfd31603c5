# 公职猫微信转发服务

## 📋 概述

公职猫微信转发服务是一个基于腾讯云轻量应用服务器的常驻服务应用，用于接收企业微信消息、下载并加密缓存文件，然后安全地转发到公职猫客户端。

## 🔧 核心功能

### 1. 企业微信消息接收
- **URL验证**: 支持企业微信Webhook配置时的URL验证
- **消息处理**: 接收并处理企业微信推送的各类消息（文本、图片、语音、视频、文件等）
- **签名验证**: 严格的消息签名验证机制，确保消息安全

### 3. 用户绑定管理
- **绑定查询**: 根据用户UUID查询绑定状态
- **自动绑定**: 通过加密令牌实现用户与企业微信的自动绑定

### 4. 消息同步
- **增量同步**: 高效的增量消息同步机制
- **多设备支持**: 支持用户多设备间的消息同步
- **状态管理**: 设备同步状态的实时更新

### 5. 安全文件下载
- **一次性令牌**: 为客户端提供一次性下载令牌
- **流式传输**: 支持断点续传和流式下载
- **自动清理**: 下载完成后立即删除缓存文件

### 2. 文件代理下载与缓存
- **文件下载**: 从企业微信API下载媒体文件
- **加密存储**: 将文件加密后临时存储在服务器本地
- **阅后即焚**: 文件下载后或3天后自动删除
- **大文件支持**: 无执行时长限制，支持大文件处理

## 🚀 API接口

### 企业微信Webhook
- `GET /api/wechat/webhook` - URL验证
- `POST /api/wechat/webhook` - 消息处理

### 用户绑定
- `GET /api/bind/status` - 查询绑定状态

### 消息同步
- `GET /api/sync/messages` - 增量同步消息（包含下载链接）
- `POST /api/sync/ack` - 确认消息同步
- `POST /api/sync/register-device` - 注册设备

### 文件下载
- `GET /api/media/download/:token` - 安全文件下载

详细的API文档请参考 [docs/API_DOCUMENTATION.md](./docs/API_DOCUMENTATION.md)

## 🛠 部署配置

### 环境变量
```bash
# 企业微信配置
WECHAT_TOKEN=your_token
WECHAT_ENCODING_AES_KEY=your_aes_key
WECHAT_CORP_ID=your_corp_id

# 数据库配置
MYSQL_HOST=**********
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=gongzhimall_wechat

# 安全配置
BINDING_SECRET=your_binding_secret

# [可选] 测试配置
# 设置为 'true' 以允许在生产环境(NODE_ENV=production)下运行集成测试
WECHAT_API_ALLOW_TEST_INJECT=false
```

### 部署步骤
1. 配置环境变量
2. 安装依赖：`npm install --production`
3. 启动服务：`npm start`
4. 验证部署：检查服务器日志和API端点

## 🔐 安全特性

- **签名验证**: 所有API请求都需要HMAC-SHA256签名验证
- **令牌加密**: 绑定令牌使用AES加密，10分钟有效期
- **环境隔离**: 生产环境强制使用环境变量，禁止硬编码
- **日志脱敏**: 敏感信息在日志中自动脱敏处理

## 📊 监控与日志

- **服务器监控**: 内存使用、响应时间、错误率监控
- **数据库日志**: 所有操作记录到MySQL系统日志表
- **错误追踪**: 详细的错误堆栈和上下文信息

## 🔄 消息处理流程

1. **接收消息**: 企业微信推送消息到Webhook
2. **验证签名**: 验证消息签名确保安全
3. **解密内容**: 使用AES解密消息内容
4. **检查绑定**: 验证用户绑定状态
5. **存储元数据**: 保存消息元数据（不存储内容）
6. **推送通知**: 通知所有绑定设备

## 📝 开发说明

### 项目结构
```
backend/wechat/
├── index.js                  # 轻量服务器入口
├── api/controller.js         # 控制器层
├── service/service.js        # 业务逻辑层
├── data/database.js          # 数据库操作
├── service/wechatApi.js      # 企业微信API
├── api/errorHandler.js       # 错误处理
├── service/pushService.js    # 推送服务
├── service/deviceManager.js  # 设备管理
```

### 开发环境
- Node.js 18+
- MySQL 8.0+
- 腾讯云轻量应用服务器


## 📞 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 文档：查看 [docs/API_DOCUMENTATION.md](./docs/API_DOCUMENTATION.md)
- 部署指南：查看 [docs/DEPLOYMENT_GUIDE.md](./docs/DEPLOYMENT_GUIDE.md) 