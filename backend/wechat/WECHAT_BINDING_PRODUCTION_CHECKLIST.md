# 微信绑定生产环境部署检查清单

## 🚨 关键问题修复说明

### 问题背景
之前系统中存在一个严重的安全隐患：当解密绑定令牌失败时，系统会检测到"test-user-uuid-12345"并硬编码替换为固定UUID，这可能导致：
- 用户绑定到错误的账号
- 数据混乱和安全问题
- 生产环境中的不可预测行为

### 已修复的问题
1. ✅ **移除临时修复代码**：删除了硬编码UUID替换逻辑
2. ✅ **增强解密验证**：添加了详细的解密过程日志和验证
3. ✅ **加密过程验证**：在加密时立即验证数据完整性
4. ✅ **防御性编程**：添加了多层参数验证和错误处理
5. ✅ **验证脚本**：创建了自动化测试脚本确保加密解密一致性

## 📋 部署前必须检查项目

### 1. 环境变量配置
- [ ] **WECHAT_BINDING_SECRET**：确保在所有环境中设置了相同的强密钥
- [ ] **密钥长度**：建议至少32个字符，包含字母、数字和特殊字符
- [ ] **密钥一致性**：开发、测试、生产环境使用相同的密钥

```bash
# 验证环境变量
echo "WECHAT_BINDING_SECRET长度: ${#WECHAT_BINDING_SECRET}"
echo "密钥前10字符: ${WECHAT_BINDING_SECRET:0:10}..."
```

### 2. 运行验证脚本
```bash
cd backend/wechat/scripts
node verify-binding-encryption.js
```
**必须确保所有测试通过！**

### 3. 数据库清理
- [ ] 清理所有可能存在的测试数据
- [ ] 检查user_bindings表中是否有异常的绑定记录
- [ ] 验证external_userid和user_uuid的数据完整性

```sql
-- 检查绑定数据完整性
SELECT 
  user_uuid,
  external_userid,
  binding_status,
  binding_time,
  COUNT(*) as count
FROM user_bindings 
GROUP BY user_uuid 
HAVING count > 1;

-- 检查是否有异常UUID
SELECT * FROM user_bindings 
WHERE user_uuid NOT REGEXP '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$';
```

### 4. 日志监控配置
- [ ] 确保生产环境日志级别设置为info或warn
- [ ] 配置关键错误的告警机制
- [ ] 设置绑定失败的监控指标

### 5. 微信配置验证
- [ ] **WECHAT_CORP_ID**：企业微信公司ID正确
- [ ] **WECHAT_CORP_SECRET**：企业微信应用密钥正确
- [ ] **WECHAT_DEFAULT_OPEN_KFID**：客服账号ID正确
- [ ] **Webhook URL**：回调地址配置正确

## 🔍 部署后验证步骤

### 1. 端到端测试
1. **生成绑定链接**：
   ```bash
   curl -X POST https://your-api.com/api/bind/one-click-link \
     -H "Content-Type: application/json" \
     -d '{"user_uuid":"test-uuid-here"}'
   ```

2. **模拟微信回调**：
   - 点击生成的链接
   - 在微信客服中发送消息
   - 验证绑定是否成功

3. **检查绑定状态**：
   ```bash
   curl "https://your-api.com/api/bind/status?user_uuid=test-uuid-here"
   ```

### 2. 监控关键指标
- [ ] 绑定成功率 > 95%
- [ ] 解密失败率 < 1%
- [ ] 响应时间 < 2秒
- [ ] 错误日志中无"test-user"相关信息

### 3. 异常情况处理
- [ ] 验证过期链接的处理
- [ ] 验证重复绑定的处理
- [ ] 验证网络异常的降级机制

## 🚨 告警配置建议

### 关键错误告警
```javascript
// 需要立即告警的错误模式
const criticalErrors = [
  "绑定链接解析失败",
  "UUID格式无效",
  "加密验证失败",
  "绑定令牌数据损坏"
];
```

### 监控指标
- **绑定成功率**：每小时统计
- **解密失败次数**：超过阈值告警
- **异常UUID出现**：立即告警
- **环境变量缺失**：启动时检查

## 📝 回滚计划

如果发现问题，按以下步骤回滚：

1. **立即停止新的绑定请求**
2. **恢复到上一个稳定版本**
3. **检查和修复数据不一致问题**
4. **重新运行验证脚本**
5. **逐步恢复服务**

## 🔧 故障排查指南

### 常见问题和解决方案

1. **解密失败**
   - 检查WECHAT_BINDING_SECRET是否一致
   - 验证数据传输过程中是否被截断
   - 检查URL编码/解码是否正确

2. **UUID格式错误**
   - 检查APP端UUID生成逻辑
   - 验证数据库中UUID的完整性
   - 确认没有特殊字符干扰

3. **绑定状态异常**
   - 检查数据库连接和事务处理
   - 验证微信回调的签名验证
   - 确认推送通知的配置

## ✅ 部署完成确认

部署完成后，请确认以下项目：

- [ ] 验证脚本测试通过
- [ ] 端到端测试成功
- [ ] 监控告警配置完成
- [ ] 日志输出正常
- [ ] 性能指标正常
- [ ] 团队已了解新的错误处理逻辑

## 📞 紧急联系方式

如果生产环境出现问题，请立即联系：
- 技术负责人：[联系方式]
- 运维团队：[联系方式]
- 微信企业服务：[联系方式]

---

**重要提醒**：此修复涉及核心安全逻辑，务必严格按照检查清单执行，确保每一步都验证通过后再进行下一步。 