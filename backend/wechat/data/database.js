/**
 * 数据库管理模块
 * 
 * 等保合规要求：
 * - 二级等保：日志保存不少于6个月
 * - 三级等保：日志保存不少于1年
 * 
 * 当前配置（符合三级等保要求）：
 * - 消息记录保存：365天（1年）
 * - 系统日志分区保存：13个月（1年+1个月缓冲）
 * - Token清理：立即清理过期Token（不涉及审计）
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const deviceManager = require('../service/deviceManager');
const wechatApi = require('../service/wechatApi');
const pushService = require('../service/pushService');

// 数据库连接配置 - 生产环境必须从环境变量获取
const dbConfig = {
  host: process.env.MYSQL_HOST,
  port: parseInt(process.env.MYSQL_PORT || '3306'),
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  charset: 'utf8mb4',
  timezone: '+08:00',
  connectionLimit: 10,
  multipleStatements: true // <-- 允许执行多个SQL语句
};

// 数据库配置验证
if (!dbConfig.host || !dbConfig.user || !dbConfig.password || !dbConfig.database) {
  throw new Error('缺少必要的数据库配置环境变量: MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE');
}

// 创建连接池
const pool = mysql.createPool(dbConfig);

/**
 * 执行查询
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise<Array>} 查询结果
 */
const query = async (sql, params = []) => {
  try {
    console.log('执行SQL:', sql);
    console.log('参数:', params);
    
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
};

/**
 * 执行事务
 * @param {Function} callback 事务回调函数
 * @returns {Promise<any>} 事务结果
 */
const transaction = async (callback) => {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    console.error('事务执行错误:', error);
    throw error;
  } finally {
    connection.release();
  }
};

/**
 * 获取单条记录
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise<Object|null>} 单条记录或null
 */
const getOne = async (sql, params = []) => {
  const rows = await query(sql, params);
  return rows.length > 0 ? rows[0] : null;
};

/**
 * 插入记录
 * @param {string} table 表名
 * @param {Object} data 数据对象
 * @returns {Promise<Object>} 插入结果
 */
const insert = async (table, data) => {
  // 过滤掉undefined值，将其转换为null
  const cleanData = {};
  Object.keys(data).forEach(key => {
    cleanData[key] = data[key] === undefined ? null : data[key];
  });
  
  const fields = Object.keys(cleanData);
  const values = Object.values(cleanData);
  const placeholders = fields.map(() => '?').join(',');
  
  const sql = `INSERT INTO ${table} (${fields.join(',')}) VALUES (${placeholders})`;
  
  try {
    const [result] = await pool.execute(sql, values);
    return {
      success: true,
      insertId: result.insertId,
      affectedRows: result.affectedRows
    };
  } catch (error) {
    console.error('插入记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 更新记录
 * @param {string} table 表名
 * @param {Object} data 更新数据
 * @param {Object} where 条件
 * @returns {Promise<Object>} 更新结果
 */
const update = async (table, data, where) => {
  const setClause = Object.keys(data).map(key => `${key} = ?`).join(',');
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  
  const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
  const params = [...Object.values(data), ...Object.values(where)];
  
  try {
    const [result] = await pool.execute(sql, params);
    return {
      success: true,
      affectedRows: result.affectedRows,
      changedRows: result.changedRows
    };
  } catch (error) {
    console.error('更新记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 删除记录
 * @param {string} table 表名
 * @param {Object} where 条件
 * @returns {Promise<Object>} 删除结果
 */
const deleteRecord = async (table, where) => {
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
  const params = Object.values(where);
  
  try {
    const [result] = await pool.execute(sql, params);
    return {
      success: true,
      affectedRows: result.affectedRows
    };
  } catch (error) {
    console.error('删除记录错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * 检查数据库连接
 * @returns {Promise<boolean>} 连接状态
 */
const checkConnection = async () => {
  try {
    await query('SELECT 1');
    console.log('✅ 数据库连接正常');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
};

/**
 * 关闭连接池
 */
const closePool = async () => {
  try {
    await pool.end();
    console.log('数据库连接池已关闭');
  } catch (error) {
    console.error('关闭连接池错误:', error);
  }
};

// ==================== 业务数据库操作函数 ====================

/**
 * 根据用户UUID查询绑定信息
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object|null>} 绑定信息或null
 */
const getBindingByUserUuid = async (userUuid) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    if (userUuid.trim().length === 0) {
      throw new Error('userUuid不能为空字符串');
    }

    const sql = `
      SELECT user_uuid, external_userid, binding_status, created_at as binding_time
      FROM wechat_bindings
      WHERE user_uuid = ? AND binding_status = 'active'
    `;
    return await getOne(sql, [userUuid]);
  } catch (error) {
    console.error('查询用户绑定信息失败:', error);
    throw error;
  }
};

/**
 * 根据external_userid查询绑定信息
 * @param {string} externalUserId 企业微信外部用户ID
 * @returns {Promise<Object|null>} 绑定信息或null
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    // 参数非空验证
    if (!externalUserId || typeof externalUserId !== 'string') {
      throw new Error('externalUserId参数无效：必须是非空字符串');
    }

    if (externalUserId.trim().length === 0) {
      throw new Error('externalUserId不能为空字符串');
    }

    const sql = `
      SELECT user_uuid, external_userid, binding_status, created_at as binding_time
      FROM wechat_bindings
      WHERE external_userid = ? AND binding_status = 'active'
    `;
    return await getOne(sql, [externalUserId]);
  } catch (error) {
    console.error('查询外部用户绑定信息失败:', error);
    throw error;
  }
};

/**
 * 创建或更新绑定关系
 * @param {string} userUuid 用户UUID
 * @param {string} externalUserId 企业微信外部用户ID
 * @returns {Promise<Object>} 操作结果
 */
const createOrUpdateBinding = async (userUuid, externalUserId) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    if (!externalUserId || typeof externalUserId !== 'string') {
      throw new Error('externalUserId参数无效：必须是非空字符串');
    }

    // 参数格式验证
    if (userUuid.trim().length === 0) {
      throw new Error('userUuid不能为空字符串');
    }

    if (externalUserId.trim().length === 0) {
      throw new Error('externalUserId不能为空字符串');
    }

    return await transaction(async (connection) => {
      // 检查是否已存在绑定
      const [existingBinding] = await connection.execute(
        'SELECT id, user_uuid, external_userid FROM wechat_bindings WHERE user_uuid = ? OR external_userid = ?',
        [userUuid, externalUserId]
      );
      
      // 日志记录
      console.log('【绑定检查】userUuid:', userUuid, 'externalUserId:', externalUserId);
      if (existingBinding.length > 0) {
        console.log('【绑定检查】找到已存在的绑定:', existingBinding);
      } else {
        console.log('【绑定检查】未找到绑定关系，将创建新绑定。');
      }

      if (existingBinding.length > 0) {
        // 更新现有绑定
        const [updateResult] = await connection.execute(`
          UPDATE wechat_bindings 
          SET user_uuid = ?, external_userid = ?, binding_status = 'active', 
              binding_token = NULL, token_expires_at = NULL, updated_at = NOW()
          WHERE id = ?
        `, [userUuid, externalUserId, existingBinding[0].id]);
        
        console.log('【绑定更新】更新结果:', updateResult);

        if (updateResult.affectedRows > 0) {
          return {
            success: true,
            binding: {
              user_uuid: userUuid,
              external_userid: externalUserId,
              binding_status: 'active',
              binding_time: new Date()
            }
          };
        }
      } else {
        // 创建新绑定
        const [insertResult] = await connection.execute(`
          INSERT INTO wechat_bindings (user_uuid, external_userid, binding_status)
          VALUES (?, ?, 'active')
        `, [userUuid, externalUserId]);
        
        console.log('【绑定创建】插入结果:', insertResult);

        if (insertResult.affectedRows > 0) {
          return {
            success: true,
            binding: {
              user_uuid: userUuid,
              external_userid: externalUserId,
              binding_status: 'active',
              binding_time: new Date()
            }
          };
        }
      }
      
      return {
        success: false,
        message: '绑定操作失败'
      };
    });
  } catch (error) {
    console.error('创建或更新绑定关系失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 删除用户绑定关系
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object>} 操作结果
 */
const deleteBinding = async (userUuid) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    console.log('【删除绑定】开始删除用户绑定:', userUuid);

    return await transaction(async (connection) => {
      // 删除绑定记录
      const [deleteResult] = await connection.execute(`
        DELETE FROM wechat_bindings
        WHERE user_uuid = ? AND binding_status = 'active'
      `, [userUuid]);

      console.log('【删除绑定】删除结果:', deleteResult);

      if (deleteResult.affectedRows > 0) {
        return {
          success: true,
          message: '绑定删除成功'
        };
      } else {
        return {
          success: false,
          message: '未找到活跃的绑定记录'
        };
      }
    });
  } catch (error) {
    console.error('删除绑定关系失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 保存消息元数据（支持新的文件缓存机制）
 * @param {Object} messageData 消息数据
 * @returns {Promise<Object>} 操作结果
 */
const saveMessageMetadata = async (messageData) => {
  try {
    const {
      user_uuid,
      external_userid,
      wechat_message_id,
      message_type,
      content,
      file_name,
      file_path,
      file_size,
      content_type,
      // 新增的文件类型字段
      file_extension,
      mime_type,
      original_mime_type,
      file_magic_detected,
      media_id,
      media_id_expires_at,
      // 位置消息字段
      location_x,
      location_y,
      location_scale,
      location_label,
      // 链接消息字段
      link_title,
      link_description,
      link_url,
      link_pic_url,
      // 事件消息字段
      event_type,
      event_key,
      event_data,
      download_token,
      download_expires_at,
      file_expires_at,
      downloaded = false,
      metadata
    } = messageData;
    
    const insertData = {
      user_uuid,
      external_userid,
      wechat_message_id,
      message_type,
      metadata: JSON.stringify(metadata || {}),
      sync_status: 'pending'
    };
    
    // 文本消息内容
    if (content) {
      insertData.content = content;
    }
    
    // 文件缓存机制
    if (file_name) {
      insertData.file_name = file_name;
    }
    if (file_path) {
      insertData.file_path = file_path;
    }
    if (file_size) {
      insertData.file_size = file_size;
    }
    if (content_type) {
      insertData.content_type = content_type;
    }

    // 新增的文件类型字段
    if (file_extension) {
      insertData.file_extension = file_extension;
    }
    if (mime_type) {
      insertData.mime_type = mime_type;
    }
    if (original_mime_type) {
      insertData.original_mime_type = original_mime_type;
    }
    if (file_magic_detected !== undefined) {
      insertData.file_magic_detected = file_magic_detected;
    }

    if (media_id) {
      insertData.media_id = media_id;
    }
    if (media_id_expires_at) {
      insertData.media_id_expires_at = media_id_expires_at;
    }

    // 位置消息字段
    if (location_x !== undefined) {
      insertData.location_x = location_x;
    }
    if (location_y !== undefined) {
      insertData.location_y = location_y;
    }
    if (location_scale !== undefined) {
      insertData.location_scale = location_scale;
    }
    if (location_label) {
      insertData.location_label = location_label;
    }

    // 链接消息字段
    if (link_title) {
      insertData.link_title = link_title;
    }
    if (link_description) {
      insertData.link_description = link_description;
    }
    if (link_url) {
      insertData.link_url = link_url;
    }
    if (link_pic_url) {
      insertData.link_pic_url = link_pic_url;
    }

    // 事件消息字段
    if (event_type) {
      insertData.event_type = event_type;
    }
    if (event_key) {
      insertData.event_key = event_key;
    }
    if (event_data) {
      insertData.event_data = JSON.stringify(event_data);
    }
    if (download_token) {
      insertData.download_token = download_token;
    }
    if (download_expires_at) {
      insertData.download_expires_at = download_expires_at;
    }
    if (file_expires_at) {
      insertData.file_expires_at = file_expires_at;
    }
    if (downloaded !== undefined) {
      insertData.downloaded = downloaded;
    }
    
    const result = await insert('wechat_message_logs', insertData);
    
    if (result.success) {
      return {
        success: true,
        message_id: result.insertId,
        download_expires_at,
        file_expires_at
      };
    } else {
      return {
        success: false,
        message: result.error
      };
    }
  } catch (error) {
    console.error('保存消息元数据失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 标记文件已下载（阅后即焚）
 * @param {string} messageId 消息ID
 * @returns {Promise<Object>} 操作结果
 */
const markFileAsDownloaded = async (messageId) => {
  try {
    const result = await query(`
      UPDATE wechat_message_logs 
      SET downloaded = TRUE, 
          downloaded_at = NOW(),
          updated_at = NOW()
      WHERE wechat_message_id = ?
    `, [messageId]);
    
    return {
      success: true,
      affected_rows: result.affectedRows
    };
  } catch (error) {
    console.error('标记文件已下载失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取增量消息
 * @param {string} userUuid 用户UUID
 * @param {string} deviceId 设备ID
 * @param {number} sinceId 起始消息ID
 * @param {number} limit 限制数量
 * @returns {Promise<Object>} 查询结果
 */
const getIncrementalMessages = async (userUuid, deviceId, sinceId, limit) => {
  try {
    // 确保参数类型正确，并进行安全验证
    const parsedSinceId = parseInt(sinceId, 10) || 0;
    let parsedLimit = parseInt(limit, 10) || 50;
    
    // 对limit进行安全限制，防止恶意查询
    if (parsedLimit <= 0) {
      parsedLimit = 50;
    }
    if (parsedLimit > 200) { // 设置一个合理的上限
      parsedLimit = 200;
    }
    
    // 为了检查是否还有更多消息，我们请求 limit + 1 条记录
    const queryLimit = parsedLimit + 1;
    
    console.log('执行消息查询:', { userUuid, deviceId, parsedSinceId, queryLimit });
    
    // 查询增量消息（包含文件类型信息）
    // 注意：LIMIT子句不能使用参数绑定，必须直接拼接，因此需要确保queryLimit是安全的整数
    const sql = `
      SELECT id, wechat_message_id, message_type, content,
             file_name, file_path, file_size, content_type,
             file_extension, mime_type, original_mime_type, file_magic_detected,
             media_id, media_id_expires_at,
             download_token, download_expires_at, file_expires_at,
             downloaded, metadata, created_at
      FROM wechat_message_logs
      WHERE user_uuid = ? AND id > ?
      ORDER BY id ASC
      LIMIT ${queryLimit}
    `;
    
    let messages = await query(sql, [userUuid, parsedSinceId]);
    
    // 检查是否还有更多消息
    const hasMore = messages.length > parsedLimit;
    if (hasMore) {
      // 移除多获取的那一条记录
      messages = messages.slice(0, parsedLimit);
    }
    
    const nextSinceId = messages.length > 0 ? messages[messages.length - 1].id : parsedSinceId;
    
    // 移除：设备同步状态的更新将由客户端的ack请求触发
    // if (messages.length > 0) {
    //   const lastMessageId = messages[messages.length - 1].id;
    //   await updateDeviceSyncStatus(userUuid, deviceId, lastMessageId);
    // }
    
    // 处理消息映射，支持新的文件下载机制
    const processedMessages = await Promise.all(messages.map(async (msg) => {
      const baseMessage = {
        id: msg.id,
        wechat_message_id: msg.wechat_message_id,
        message_type: msg.message_type,
        metadata: typeof msg.metadata === 'string' ? JSON.parse(msg.metadata || '{}') : msg.metadata,
        created_at: msg.created_at
      };

      // 处理文本消息
      if (msg.message_type === 'text' && msg.content) {
        baseMessage.content = msg.content;
        return baseMessage;
      }

      // 处理文件类消息（图片、语音、视频、文件）
      if (['image', 'voice', 'video', 'file'].includes(msg.message_type)) {
        // 添加媒体ID信息（用于移动端识别需要下载的媒体）
        if (msg.media_id) {
          baseMessage.media_id = msg.media_id;
          baseMessage.media_id_expires_at = msg.media_id_expires_at;
        }

        // 优先使用新的文件下载机制
        if (msg.download_token && msg.file_name) {
          // 检查下载链接是否过期
          const now = new Date();
          const downloadExpiresAt = new Date(msg.download_expires_at);
          
          if (downloadExpiresAt > now && !msg.downloaded) {
            // 下载链接仍然有效
            baseMessage.download_url = `/api/media/download/${msg.download_token}`;
            baseMessage.download_expires_at = msg.download_expires_at;
            baseMessage.file_name = msg.file_name;
            baseMessage.file_size = msg.file_size;
            baseMessage.content_type = msg.content_type;
          } else if (msg.file_path && !msg.downloaded) {
            // 下载链接过期，但文件仍存在，需要重新生成链接
            try {
              const fileService = require('../service/fileService');
              const newFileInfo = await fileService.regenerateDownloadLink({
                id: msg.wechat_message_id,
                file_name: msg.file_name,
                file_path: msg.file_path,
                content_type: msg.content_type,
                file_size: msg.file_size
              });
              
              if (newFileInfo) {
                // 更新数据库中的下载令牌
                await query(`
                  UPDATE wechat_message_logs 
                  SET download_token = ?, download_expires_at = ? 
                  WHERE id = ?
                `, [newFileInfo.downloadToken, newFileInfo.downloadExpiresAt, msg.id]);
                
                baseMessage.download_url = newFileInfo.downloadUrl;
                baseMessage.download_expires_at = newFileInfo.downloadExpiresAt;
                baseMessage.file_name = msg.file_name;
                baseMessage.file_size = msg.file_size;
                baseMessage.content_type = msg.content_type;
              } else {
                // 文件不存在，标记为已过期
                baseMessage.file_expired = true;
                baseMessage.error_message = '文件已过期或不存在';
              }
            } catch (error) {
              console.error('重新生成下载链接失败:', error);
              baseMessage.file_expired = true;
              baseMessage.error_message = '文件处理失败';
            }
          } else {
            // 文件已下载或已过期
            baseMessage.file_expired = true;
            baseMessage.error_message = msg.downloaded ? '文件已下载' : '下载链接已过期';
          }
        } else {
          // 文件信息不完整
          baseMessage.file_expired = true;
          baseMessage.error_message = '文件信息缺失';
        }
      }

      return baseMessage;
    }));

    return {
      success: true,
      messages: processedMessages,
      has_more: hasMore,
      next_since_id: nextSinceId
    };
  } catch (error) {
    console.error('获取增量消息失败:', error);
    return {
      success: false,
      message: error.message,
      messages: [],
      has_more: false,
      next_since_id: sinceId // 返回原始的since_id
    };
  }
};

/**
 * 更新设备同步状态
 * @param {string} userUuid 用户UUID
 * @param {string} deviceId 设备ID
 * @param {number} lastSyncedId 最后同步的消息ID
 * @returns {Promise<Object>} 更新结果
 */
const updateDeviceSyncStatus = async (userUuid, deviceId, lastSyncedId) => {
  try {
    const result = await update(
      'user_device_bindings',
      {
        last_synced_id: lastSyncedId,
        last_active_at: new Date()
      },
      {
        user_uuid: userUuid,
        device_id: deviceId
      }
    );
    
    return result;
  } catch (error) {
    console.error('更新设备同步状态失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 注册设备
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise<Object>} 注册结果
 */
const registerDevice = async (deviceInfo) => {
  try {
    // 验证设备信息
    const validation = deviceManager.validateDeviceInfo(deviceInfo);
    if (!validation.valid) {
      return {
        success: false,
        message: `设备信息验证失败: ${validation.errors.join(', ')}`
      };
    }
    
    // 标准化设备信息
    const normalizedInfo = deviceManager.normalizeDeviceInfo(deviceInfo);
    
    const {
      user_uuid,
      device_id,
      device_name,
      platform,
      platform_version,
      app_version,
      device_model,
      push_token,
      push_provider,
      device_fingerprint
    } = normalizedInfo;
    
    // 首先确保用户记录存在
    const existingUser = await getOne(
      'SELECT user_uuid FROM app_users WHERE user_uuid = ?',
      [user_uuid]
    );

    if (!existingUser) {
      // 创建用户记录
      console.log('创建新用户记录:', user_uuid);
      const userResult = await insert('app_users', {
        user_uuid,
        first_device_id: device_id,
        device_count: 1,
        is_wechat_bound: false
      });

      if (!userResult.success) {
        console.error('创建用户记录失败:', userResult.error);
        return {
          success: false,
          message: '创建用户记录失败'
        };
      }
    }

    // 检查设备是否已存在
    const existingDevice = await getOne(
      'SELECT id, device_status FROM user_device_bindings WHERE user_uuid = ? AND device_id = ?',
      [user_uuid, device_id]
    );
    
    if (existingDevice) {
      // 更新现有设备 - 确保undefined转换为null
      const updateData = {
        device_name: device_name || null,
        platform: platform || null,
        platform_version: platform_version || null,
        app_version: app_version || null,
        device_model: device_model || null,
        push_token: push_token || null,
        push_provider: push_provider || null,
        device_fingerprint: device_fingerprint || null,
        last_active_at: new Date(),
        device_status: 'active'
      };

      console.log('更新设备数据:', updateData);

      const result = await update(
        'user_device_bindings',
        updateData,
        {
          user_uuid,
          device_id
        }
      );
      
      if (result.success) {
        return {
          success: true,
          device: {
            id: existingDevice.id,
            user_uuid,
            device_id,
            device_name,
            platform,
            platform_version,
            app_version,
            device_model,
            push_token,
            push_provider,
            device_fingerprint,
            device_status: 'active',
            sync_strategy: deviceManager.getSyncStrategy(platform)
          }
        };
      }
    } else {
      // 创建新设备 - 确保undefined转换为null
      const insertData = {
        user_uuid,
        device_id,
        device_name: device_name || null,
        platform: platform || null,
        platform_version: platform_version || null,
        app_version: app_version || null,
        device_model: device_model || null,
        push_token: push_token || null,
        push_provider: push_provider || null,
        device_fingerprint: device_fingerprint || null,
        device_status: 'active'
      };

      console.log('插入设备数据:', insertData);

      const result = await insert('user_device_bindings', insertData);
      
      if (result.success) {
        console.log('设备注册成功:', result.insertId);

        // 更新用户的设备计数
        await query(
          'UPDATE app_users SET device_count = (SELECT COUNT(*) FROM user_device_bindings WHERE user_uuid = ?) WHERE user_uuid = ?',
          [user_uuid, user_uuid]
        );

        return {
          success: true,
          device: {
            id: result.insertId,
            user_uuid,
            device_id,
            device_name,
            platform,
            platform_version,
            app_version,
            device_model,
            push_token,
            push_provider,
            device_fingerprint,
            device_status: 'active',
            sync_strategy: deviceManager.getSyncStrategy(platform)
          }
        };
      } else {
        console.error('设备注册失败:', result.error);
        return {
          success: false,
          message: `设备注册失败: ${result.error}`
        };
      }
    }
    
    console.error('设备注册流程异常结束');
    return {
      success: false,
      message: '设备注册流程异常结束'
    };
  } catch (error) {
    console.error('注册设备失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 推送消息到设备（通过JPush）
 * @param {string} userUuid 用户UUID
 * @param {number} messageId 消息ID
 * @returns {Promise<Object>} 推送结果
 */
const pushMessageToDevices = async (userUuid, messageId) => {
  try {
    // 获取用户所有活跃设备
    const devices = await query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active' AND push_token IS NOT NULL
    `, [userUuid]);
    
    if (devices.length === 0) {
      return {
        success: true,
        message: '没有活跃设备需要推送'
      };
    }
    
    // 获取完整的消息信息（包含文件类型信息）
    const messageInfo = await getOne(`
      SELECT id, message_type, content, file_name, file_size,
             mime_type, file_extension, metadata
      FROM wechat_message_logs
      WHERE id = ?
    `, [messageId]);

    if (!messageInfo) {
      return {
        success: false,
        message: '消息不存在'
      };
    }

    // 解析metadata
    let parsedMetadata = {};
    try {
      parsedMetadata = messageInfo.metadata ? JSON.parse(messageInfo.metadata) : {};
    } catch (error) {
      console.warn('解析消息metadata失败:', error);
    }

    // 调用推送服务发送智能消息通知
    const pushResult = await pushService.sendMessageNotification(devices, {
      message_id: messageId,
      message_type: messageInfo.message_type,
      content: messageInfo.content,
      file_name: messageInfo.file_name,
      file_size: messageInfo.file_size,
      mime_type: messageInfo.mime_type,
      file_extension: messageInfo.file_extension,
      metadata: parsedMetadata
    });
    
    return pushResult;
  } catch (error) {
    console.error('推送消息到设备失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 推送下载指令到设备（通过JPush）
 * @param {string} userUuid 用户UUID
 * @param {Object} downloadInstruction 下载指令
 * @returns {Promise<Object>} 推送结果
 */
const pushDownloadInstructionToDevices = async (userUuid, downloadInstruction) => {
  try {
    console.log('🔔 开始推送下载指令:', { userUuid, downloadInstruction });

    // 获取用户所有活跃设备
    const devices = await query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active' AND push_token IS NOT NULL
    `, [userUuid]);

    console.log('📱 找到活跃设备:', devices.length, devices);

    if (devices.length === 0) {
      console.log('⚠️ 没有活跃设备需要推送');
      return {
        success: true,
        message: '没有活跃设备需要推送'
      };
    }

    // 调用推送服务发送下载指令通知
    const pushMessage = {
      title: '收到新文件',
      content: downloadInstruction.file_name ? `收到文件: ${downloadInstruction.file_name}` : '您有新的微信转发文件',
      data: {
        type: 'wechat_file_download',
        message_id: downloadInstruction.message_id,
        message_type: downloadInstruction.message_type,
        media_id: downloadInstruction.media_id,
        expires_at: downloadInstruction.expires_at,
        file_name: downloadInstruction.file_name,
        file_size: downloadInstruction.file_size,
        user_uuid: userUuid,
        timestamp: Date.now()
      }
    };

    console.log('📤 准备发送推送:', pushMessage);

    const pushResult = await pushService.sendPushNotification(devices, pushMessage);

    console.log('📬 推送结果:', pushResult);

    return pushResult;
  } catch (error) {
    console.error('推送下载指令到设备失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};


/**
 * 获取短期访问令牌
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object>} 令牌结果
 */
const getShortTermAccessToken = async (userUuid) => {
  try {
    // 直接调用重构后的wechatApi函数
    const result = await wechatApi.generateAndStoreShortTermToken(userUuid);
    return result;
  } catch (error) {
    console.error('获取短期访问令牌失败 (database.js):', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 存储短期令牌到数据库
 */
const storeShortTermToken = async (tokenData) => {
  try {
    const { user_uuid, token, access_token, expires_at } = tokenData;

    // 创建短期令牌存储表（如果不存在）
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS short_term_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_uuid VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        access_token TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_uuid (user_uuid),
        INDEX idx_token (token),
        INDEX idx_expires_at (expires_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await query(createTableQuery);

    // 删除过期的令牌
    const cleanupQuery = `
      DELETE FROM short_term_tokens
      WHERE expires_at < NOW()
    `;
    await query(cleanupQuery);

    // 存储新令牌
    const insertQuery = `
      INSERT INTO short_term_tokens (user_uuid, token, access_token, expires_at)
      VALUES (?, ?, ?, ?)
    `;

    const result = await query(insertQuery, [user_uuid, token, access_token, expires_at]);

    return {
      success: true,
      token_id: result.insertId
    };
  } catch (error) {
    console.error('存储短期令牌失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取或创建access_token缓存表
 */
const ensureAccessTokenCacheTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS access_token_cache (
      id INT AUTO_INCREMENT PRIMARY KEY,
      corp_id VARCHAR(255) NOT NULL UNIQUE,
      access_token TEXT NOT NULL,
      expires_at DATETIME NOT NULL,
      is_refreshing TINYINT(1) DEFAULT 0,
      refresh_started_at DATETIME NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_corp_id (corp_id),
      INDEX idx_expires_at (expires_at),
      INDEX idx_corp_refreshing (corp_id, is_refreshing)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 获取或创建客服链接缓存表
 */
const ensureKfLinkCacheTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS kf_link_cache (
      id INT AUTO_INCREMENT PRIMARY KEY,
      open_kfid VARCHAR(255) NOT NULL,
      scene VARCHAR(64) NOT NULL,
      base_url TEXT NOT NULL,
      expires_at DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_kf_scene (open_kfid, scene),
      INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 从数据库获取客服链接缓存
 */
const getKfLinkFromCache = async (openKfId, scene) => {
  try {
    await ensureKfLinkCacheTable();

    const cacheQuery = `
      SELECT base_url, expires_at
      FROM kf_link_cache
      WHERE open_kfid = ? AND scene = ? AND expires_at > NOW()
    `;

    const result = await query(cacheQuery, [openKfId, scene]);

    if (result.length > 0) {
      console.log('✅ 从缓存获取客服链接成功');
      return {
        success: true,
        url: result[0].base_url,
        cached: true
      };
    } else {
      console.log('ℹ️ 客服链接缓存未命中或已过期');
      return {
        success: false,
        message: '缓存未命中'
      };
    }
  } catch (error) {
    console.error('❌ 获取客服链接缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 保存客服链接到缓存
 * 缓存有效期与access_token同步，确保链接有效性
 */
const saveKfLinkToCache = async (openKfId, scene, baseUrl, accessTokenExpiresAt) => {
  try {
    await ensureKfLinkCacheTable();

    // 客服链接的有效期应该与access_token同步，但提前5分钟过期以确保安全
    const expiresAt = new Date(accessTokenExpiresAt);
    expiresAt.setMinutes(expiresAt.getMinutes() - 5); // 提前5分钟过期

    // 确保不会设置过去的时间
    const now = new Date();
    if (expiresAt <= now) {
      console.warn('⚠️ access_token即将过期，不缓存客服链接');
      return { success: false, message: 'access_token即将过期' };
    }

    const insertQuery = `
      INSERT INTO kf_link_cache (open_kfid, scene, base_url, expires_at)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        base_url = VALUES(base_url),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
    `;

    await query(insertQuery, [openKfId, scene, baseUrl, expiresAt]);
    console.log('✅ 客服链接已保存到缓存，过期时间:', expiresAt.toISOString());

    return { success: true };
  } catch (error) {
    console.error('❌ 保存客服链接缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 尝试获取access_token刷新锁
 * 防止多个云函数实例同时刷新token
 */
const tryAcquireTokenRefreshLock = async (corpId) => {
  try {
    await ensureAccessTokenCacheTable();

    // 检查是否有其他实例正在刷新（超过2分钟的锁视为过期）
    const checkQuery = `
      SELECT is_refreshing, refresh_started_at
      FROM access_token_cache
      WHERE corp_id = ? AND is_refreshing = 1 AND refresh_started_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    `;

    const existingLock = await query(checkQuery, [corpId]);
    if (existingLock.length > 0) {
      console.log('⏳ 其他实例正在刷新access_token，等待中...');
      return { success: false, message: '其他实例正在刷新' };
    }

    // 尝试获取锁
    const lockQuery = `
      INSERT INTO access_token_cache (corp_id, access_token, expires_at, is_refreshing, refresh_started_at)
      VALUES (?, '', '1970-01-01 00:00:00', 1, NOW())
      ON DUPLICATE KEY UPDATE
        is_refreshing = 1,
        refresh_started_at = NOW()
    `;

    await query(lockQuery, [corpId]);
    console.log('🔒 获取access_token刷新锁成功');
    return { success: true };
  } catch (error) {
    console.error('❌ 获取access_token刷新锁失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 释放access_token刷新锁
 */
const releaseTokenRefreshLock = async (corpId) => {
  try {
    const unlockQuery = `
      UPDATE access_token_cache
      SET is_refreshing = 0, refresh_started_at = NULL
      WHERE corp_id = ?
    `;

    await query(unlockQuery, [corpId]);
    console.log('🔓 释放access_token刷新锁成功');
    return { success: true };
  } catch (error) {
    console.error('❌ 释放access_token刷新锁失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 从数据库获取access_token缓存
 */
const getAccessTokenFromCache = async (corpId) => {
  try {
    await ensureAccessTokenCacheTable();

    const cacheQuery = `
      SELECT access_token, expires_at
      FROM access_token_cache
      WHERE corp_id = ? AND expires_at > NOW()
    `;

    const result = await query(cacheQuery, [corpId]);

    if (result.length > 0) {
      const cache = result[0];
      console.log('✅ 从数据库缓存获取access_token成功');
      return {
        success: true,
        access_token: cache.access_token,
        expires_at: cache.expires_at
      };
    }

    return {
      success: false,
      message: '缓存中没有有效的access_token'
    };
  } catch (error) {
    console.error('从缓存获取access_token失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 将access_token存储到数据库缓存
 */
const storeAccessTokenToCache = async (corpId, accessToken, expiresIn) => {
  try {
    await ensureAccessTokenCacheTable();

    // 计算过期时间（提前5分钟过期）
    const expiresAt = new Date(Date.now() + (expiresIn - 300) * 1000);

    const upsertQuery = `
      INSERT INTO access_token_cache (corp_id, access_token, expires_at, is_refreshing, refresh_started_at)
      VALUES (?, ?, ?, 0, NULL)
      ON DUPLICATE KEY UPDATE
        access_token = VALUES(access_token),
        expires_at = VALUES(expires_at),
        is_refreshing = 0,
        refresh_started_at = NULL,
        updated_at = NOW()
    `;

    await query(upsertQuery, [corpId, accessToken, expiresAt]);

    console.log('✅ access_token已存储到数据库缓存');
    return {
      success: true,
      expires_at: expiresAt
    };
  } catch (error) {
    console.error('存储access_token到缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 记录access_token API调用
 */
const recordTokenApiCall = async (corpId) => {
  try {
    // 确保调用记录表存在
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS token_api_calls (
        id INT AUTO_INCREMENT PRIMARY KEY,
        corp_id VARCHAR(255) NOT NULL,
        call_date DATE NOT NULL,
        call_count INT DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_corp_date (corp_id, call_date),
        INDEX idx_call_date (call_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await query(createTableQuery);

    // 记录今日调用
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    const recordQuery = `
      INSERT INTO token_api_calls (corp_id, call_date, call_count)
      VALUES (?, ?, 1)
      ON DUPLICATE KEY UPDATE
        call_count = call_count + 1,
        updated_at = NOW()
    `;

    await query(recordQuery, [corpId, today]);

    // 获取今日调用次数
    const countQuery = `
      SELECT call_count
      FROM token_api_calls
      WHERE corp_id = ? AND call_date = ?
    `;

    const result = await query(countQuery, [corpId, today]);
    const todayCount = result.length > 0 ? result[0].call_count : 0;

    console.log(`📊 今日access_token API调用次数: ${todayCount}/400`);

    return {
      success: true,
      todayCount: todayCount,
      limit: 400,
      remaining: 400 - todayCount
    };
  } catch (error) {
    console.error('❌ 记录access_token API调用失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 检查今日access_token API调用是否超限
 */
const checkTokenApiLimit = async (corpId) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const countQuery = `
      SELECT call_count
      FROM token_api_calls
      WHERE corp_id = ? AND call_date = ?
    `;

    const result = await query(countQuery, [corpId, today]);
    const todayCount = result.length > 0 ? result[0].call_count : 0;

    const limit = 400;
    const isOverLimit = todayCount >= limit;

    if (isOverLimit) {
      console.warn(`⚠️ access_token API调用已达每日限制: ${todayCount}/${limit}`);
    }

    return {
      success: true,
      todayCount: todayCount,
      limit: limit,
      remaining: limit - todayCount,
      isOverLimit: isOverLimit
    };
  } catch (error) {
    console.error('❌ 检查access_token API调用限制失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 清理过期的access_token缓存
 */
const cleanupExpiredAccessTokens = async () => {
  try {
    await ensureAccessTokenCacheTable();

    const cleanupQuery = `
      DELETE FROM access_token_cache
      WHERE expires_at < NOW()
    `;

    const result = await query(cleanupQuery);

    if (result.affectedRows > 0) {
      console.log(`🗑️ 清理了 ${result.affectedRows} 个过期的access_token`);
    }

    return {
      success: true,
      cleaned_count: result.affectedRows
    };
  } catch (error) {
    console.error('清理过期access_token失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 确保客服同步游标表存在
 */
const ensureKfSyncCursorsTable = async () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS kf_sync_cursors (
      id INT AUTO_INCREMENT PRIMARY KEY,
      open_kfid VARCHAR(255) NOT NULL UNIQUE,
      last_cursor VARCHAR(255),
      last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_open_kfid (open_kfid),
      INDEX idx_last_sync_at (last_sync_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  await query(createTableQuery);
};

/**
 * 获取客服账号的同步游标
 */
const getKfSyncCursor = async (openKfId) => {
  try {
    await ensureKfSyncCursorsTable();

    const cursorQuery = `
      SELECT last_cursor, last_sync_at
      FROM kf_sync_cursors
      WHERE open_kfid = ?
    `;

    const result = await query(cursorQuery, [openKfId]);

    if (result.length > 0) {
      return {
        success: true,
        cursor: result[0].last_cursor,
        last_sync_at: result[0].last_sync_at
      };
    }

    return {
      success: true,
      cursor: null, // 首次同步
      last_sync_at: null
    };
  } catch (error) {
    console.error('获取同步游标失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 更新客服账号的同步游标
 */
const updateKfSyncCursor = async (openKfId, nextCursor) => {
  try {
    await ensureKfSyncCursorsTable();

    const upsertQuery = `
      INSERT INTO kf_sync_cursors (open_kfid, last_cursor, last_sync_at)
      VALUES (?, ?, NOW())
      ON DUPLICATE KEY UPDATE
        last_cursor = VALUES(last_cursor),
        last_sync_at = VALUES(last_sync_at),
        updated_at = NOW()
    `;

    await query(upsertQuery, [openKfId, nextCursor]);

    console.log(`✅ 更新客服账号 ${openKfId} 的同步游标: ${nextCursor}`);
    return {
      success: true
    };
  } catch (error) {
    console.error('更新同步游标失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 确保数据库表和服务就绪
 * 从 database.sql 文件确保所有表和基础数据都已创建
 */
async function ensureTablesFromSql() {
  let connection;
  try {
    console.log('🚀 [DB] 正在确保数据库表结构 (逐条执行模式)...');
    
    // multipleStatements 必须为 true
    connection = await mysql.createConnection(dbConfig);

    const sqlPath = path.resolve(__dirname, 'database.sql');
    if (!fs.existsSync(sqlPath)) {
      console.log('⚠️ [DB] database.sql 文件不存在，跳过SQL初始化');
      return;
    }

    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // 将SQL脚本分割成独立的语句
    // 这是一个相对简单的分割，但对于我们的SQL文件是有效的
    const statements = sqlContent.split(';').map(s => s.trim()).filter(s => s.length > 0);
    
    console.log(`[DB] 在 database.sql 中找到 ${statements.length} 条SQL语句，将开始逐条执行...`);

    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i];
      if (stmt) {
        console.log(`[DB] 正在执行语句 ${i + 1}/${statements.length}: ${stmt.substring(0, 90)}...`);
        await connection.query(stmt);
        console.log(`[DB] -> 语句 ${i + 1} 执行成功。`);
      }
    }
    
    console.log('✅ [DB] 所有SQL语句执行完毕。数据库表结构和基础数据已成功同步！');

  } catch (error) {
    console.error('❌ [DB] 初始化数据库时发生严重错误:', error);
    // 抛出错误以中断启动，防止服务在不完整的数据库结构上运行
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 [DB] 初始化连接已关闭');
    }
  }
}

/**
 * 动态管理system_logs表的分区
 * 自动创建未来3个月的分区，删除6个月前的分区
 */
async function manageLogPartitions() {
  console.log('🚀 [DB] 开始管理日志分区...');
  let connection;
  try {
    connection = await pool.getConnection();
    
    // 首先检查表是否已经分区
    const [partitions] = await connection.query(`
      SELECT PARTITION_NAME, PARTITION_DESCRIPTION 
      FROM INFORMATION_SCHEMA.PARTITIONS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'system_logs' AND PARTITION_NAME IS NOT NULL
    `, [process.env.DB_NAME || 'gongzhimall_wechat']);
    
    // 如果表还没有分区，先添加分区
    if (partitions.length === 0) {
      console.log('🔧 [DB] 表未分区，开始添加分区...');
      
      // 获取当前年月
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1; // 0-based转1-based
      
      // 创建基于年份的分区（简单稳定，符合等保要求）
      const partitionStatements = [];
      for (let i = 0; i < 3; i++) {
        const year = currentYear + i;
        const partitionName = `p${year}`;
        const partitionValue = year + 1;
        
        partitionStatements.push(`PARTITION ${partitionName} VALUES LESS THAN (${partitionValue})`);
      }
      
      // 添加未来分区
      partitionStatements.push('PARTITION p_future VALUES LESS THAN MAXVALUE');
      
      const alterSql = `
        ALTER TABLE system_logs 
        PARTITION BY RANGE (log_year) (
          ${partitionStatements.join(',\n          ')}
        )
      `;
      
      await connection.query(alterSql);
      console.log('✅ [DB] 分区添加完成');
    } else {
      console.log('📊 [DB] 表已分区，检查是否需要新增分区...');
      
      // 检查是否需要新增未来年份分区
      const now = new Date();
      const nextYear = now.getFullYear() + 1;
      const nextPartitionName = `p${nextYear}`;
      
      const existingPartition = partitions.find(p => p.PARTITION_NAME === nextPartitionName);
      if (!existingPartition) {
        console.log(`🔧 [DB] 需要创建新分区: ${nextPartitionName}`);
        
        // 重新组织分区，添加新的年份分区
        const partitionValue = nextYear + 1;
        
        await connection.query(`
          ALTER TABLE system_logs 
          REORGANIZE PARTITION p_future INTO (
            PARTITION ${nextPartitionName} VALUES LESS THAN (${partitionValue}),
            PARTITION p_future VALUES LESS THAN MAXVALUE
          )
        `);
        
        console.log(`✅ [DB] 新分区 ${nextPartitionName} 创建完成`);
      }
      
      // 清理2年前的分区（符合三级等保要求，保留1年+1年缓冲）
      const twoYearsAgo = now.getFullYear() - 2;
      const oldPartitionName = `p${twoYearsAgo}`;
      
      const oldPartition = partitions.find(p => p.PARTITION_NAME === oldPartitionName);
      if (oldPartition) {
        console.log(`🗑️ [DB] 清理过期分区: ${oldPartitionName}（等保合规：保留1年+缓冲）`);
        await connection.query(`ALTER TABLE system_logs DROP PARTITION ${oldPartitionName}`);
        console.log(`✅ [DB] 过期分区 ${oldPartitionName} 清理完成`);
      }
    }
    
    console.log('✅ [DB] 分区管理完成');
  } catch (error) {
    console.error('❌ [DB] 分区管理失败:', error);
    // 分区管理失败不应该影响服务启动
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * 清理过期数据（替代存储过程）
 */
async function cleanupExpiredData() {
  console.log('🧹 [DB] 开始清理过期数据...');
  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();
    
    // 清理1年前的消息索引（符合三级等保要求）
    const [result1] = await connection.query(`
      DELETE FROM wechat_message_logs 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL 365 DAY)
      AND sync_status = 'synced'
    `);
    console.log(`🗑️ [DB] 清理了 ${result1.affectedRows} 条过期消息记录`);
    
    // 清理过期的绑定Token
    const [result2] = await connection.query(`
      UPDATE wechat_bindings 
      SET binding_token = NULL, token_expires_at = NULL
      WHERE token_expires_at < NOW()
    `);
    console.log(`🔄 [DB] 清理了 ${result2.affectedRows} 个过期Token`);
    
    // 清理过期的MediaId记录
    const [result3] = await connection.query(`
      UPDATE wechat_message_logs 
      SET media_id = NULL, media_id_expires_at = NULL
      WHERE media_id_expires_at < NOW()
    `);
    console.log(`📎 [DB] 清理了 ${result3.affectedRows} 个过期MediaId`);
    
    await connection.commit();
    console.log('✅ [DB] 过期数据清理完成');
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('❌ [DB] 清理过期数据失败:', error);
    // 清理失败不应该影响服务启动
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

// 修改原有的ensureTablesFromSql函数，添加清理功能
const originalEnsureTablesFromSql = ensureTablesFromSql;
ensureTablesFromSql = async function() {
  await originalEnsureTablesFromSql();
  // 在表创建完成后管理分区
  await manageLogPartitions();
  // 清理过期数据
  await cleanupExpiredData();
};

module.exports = {
  // 基础数据库操作
  query,
  transaction,
  
  // 数据库初始化
  ensureTablesFromSql,
  manageLogPartitions,
  cleanupExpiredData,
  
  // 业务数据库操作
  getBindingByUserUuid,
  getBindingByExternalUserId,
  createOrUpdateBinding,
  deleteBinding,
  saveMessageMetadata,
  markFileAsDownloaded,
  getIncrementalMessages,
  updateDeviceSyncStatus,
  registerDevice,
  pushMessageToDevices,
  pushDownloadInstructionToDevices,
  getShortTermAccessToken,
  storeShortTermToken,

  // access_token缓存相关
  getAccessTokenFromCache,
  storeAccessTokenToCache,
  cleanupExpiredAccessTokens,
  tryAcquireTokenRefreshLock,
  releaseTokenRefreshLock,
  recordTokenApiCall,
  checkTokenApiLimit,

  // 客服链接缓存相关
  getKfLinkFromCache,
  saveKfLinkToCache,

  // 客服同步游标相关
  getKfSyncCursor,
  updateKfSyncCursor,
}; 