-- 微信转发服务数据库表结构
-- 适用于腾讯云MySQL 8.0

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS gongzhimall_wechat 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE gongzhimall_wechat;

-- 用户注册表（设备注册时创建，独立于微信绑定）
CREATE TABLE IF NOT EXISTS app_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    first_device_id VARCHAR(128) COMMENT '首次注册的设备ID',
    device_count INT DEFAULT 0 COMMENT '设备数量',
    is_wechat_bound BOOLEAN DEFAULT FALSE COMMENT '是否已绑定微信',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_is_wechat_bound (is_wechat_bound),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用用户表';

-- 微信绑定表（只存储已绑定的用户）
CREATE TABLE IF NOT EXISTS wechat_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NOT NULL COMMENT '企业微信外部用户ID',
    binding_status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '绑定状态',
    binding_token VARCHAR(255) NULL COMMENT '绑定Token',
    token_expires_at TIMESTAMP NULL COMMENT 'Token过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    UNIQUE KEY uk_external_userid (external_userid),
    INDEX idx_binding_status (binding_status),
    INDEX idx_binding_token (binding_token),
    INDEX idx_created_at (created_at),

    -- 外键约束
    FOREIGN KEY fk_wechat_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信绑定关系表';

-- 用户设备绑定表（支持跨端增量同步）
CREATE TABLE IF NOT EXISTS user_device_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    device_id VARCHAR(128) NOT NULL COMMENT '设备唯一标识',
    device_name VARCHAR(255) COMMENT '设备名称',
    platform VARCHAR(50) NOT NULL COMMENT '设备平台（ios, android, desktop, web, harmonyos, etc.）',
    platform_version VARCHAR(50) COMMENT '平台版本号',
    app_version VARCHAR(50) COMMENT '应用版本',
    device_model VARCHAR(100) COMMENT '设备型号',
    last_synced_id BIGINT DEFAULT 0 COMMENT '该设备已同步的最新消息ID',
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    device_status ENUM('active', 'inactive', 'blocked') DEFAULT 'active' COMMENT '设备状态',
    push_token VARCHAR(255) COMMENT 'JPush推送Token',
    push_provider VARCHAR(50) DEFAULT 'jpush' COMMENT '推送服务提供商（jpush, apns, fcm, hms等）',
    device_fingerprint VARCHAR(255) COMMENT '设备指纹（用于安全验证）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_device (user_uuid, device_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_device_id (device_id),
    INDEX idx_platform (platform),
    INDEX idx_last_synced_id (last_synced_id),
    INDEX idx_last_active_at (last_active_at),
    INDEX idx_device_status (device_status),
    INDEX idx_push_provider (push_provider),

    -- 外键约束
    FOREIGN KEY fk_device_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备绑定表（支持跨端增量同步）';

-- 系统日志表（适配轻量服务器环境，使用简单的年份分区）
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    log_level ENUM('ERROR', 'WARN', 'INFO', 'DEBUG') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(100) COMMENT '模块名称',
    function_name VARCHAR(100) COMMENT '服务名称',
    request_id VARCHAR(100) COMMENT '请求ID（服务器RequestId）',
    user_uuid VARCHAR(36) COMMENT '用户UUID',
    external_userid VARCHAR(128) COMMENT '企业微信用户ID',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_stack TEXT COMMENT '错误堆栈',
    metadata JSON COMMENT '额外的元数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT 'User-Agent',
    execution_time INT COMMENT '执行时间（毫秒）',
    memory_usage INT COMMENT '内存使用量（MB）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_year INT NOT NULL DEFAULT (YEAR(CURRENT_TIMESTAMP)) COMMENT '日志年份（用于分区）',
    
    -- 复合主键，包含分区字段
    PRIMARY KEY (id, log_year),
    
    -- 索引
    INDEX idx_log_level (log_level),
    INDEX idx_module (module),
    INDEX idx_function_name (function_name),
    INDEX idx_request_id (request_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_error_code (error_code),
    INDEX idx_created_at (created_at),
    INDEX idx_log_year (log_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表（适配轻量服务器环境）';

-- 微信消息索引表（支持临时文件缓存和阅后即焚机制）
CREATE TABLE IF NOT EXISTS wechat_message_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NOT NULL COMMENT '企业微信外部用户ID',
    wechat_message_id VARCHAR(128) NOT NULL COMMENT '微信消息ID（用于去重）',
    message_type ENUM('text', 'image', 'voice', 'video', 'file', 'location', 'link') NOT NULL COMMENT '消息类型',
    
    -- 文本消息内容
    content TEXT NULL COMMENT '文本消息内容',
    
    -- 文件缓存机制
    file_name VARCHAR(500) NULL COMMENT '文件名称',
    file_path VARCHAR(1000) NULL COMMENT '加密文件在服务器上的存储路径',
    file_size BIGINT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(200) NULL COMMENT '文件MIME类型',
    download_token TEXT NULL COMMENT '下载令牌（JWT）',
    download_expires_at TIMESTAMP NULL COMMENT '下载链接过期时间（24小时）',
    file_expires_at TIMESTAMP NULL COMMENT '文件过期时间（3天后删除）',
    downloaded BOOLEAN DEFAULT FALSE COMMENT '是否已被用户下载（阅后即焚标记）',
    downloaded_at TIMESTAMP NULL COMMENT '下载时间',
    
    -- 同步状态
    metadata JSON COMMENT '消息元数据（文件名、大小等非隐私信息）',
    sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending' COMMENT '同步状态',
    sync_attempts INT DEFAULT 0 COMMENT '同步尝试次数',
    last_sync_at TIMESTAMP NULL COMMENT '最后同步时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_wechat_message_id (wechat_message_id),
    INDEX idx_message_type (message_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_download_expires (download_expires_at),
    INDEX idx_file_expires (file_expires_at),
    INDEX idx_downloaded (downloaded),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束防止消息重复
    UNIQUE KEY uk_wechat_message_id (wechat_message_id),
    
    -- 外键约束
    FOREIGN KEY fk_binding (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信消息索引表（支持临时文件缓存和阅后即焚机制）';

-- 同步配置表
CREATE TABLE IF NOT EXISTS wechat_sync_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    sync_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用同步',
    sync_types JSON COMMENT '同步的消息类型配置',
    sync_frequency INT DEFAULT 300 COMMENT '同步频率（秒）',
    last_sync_time TIMESTAMP NULL COMMENT '最后同步时间',
    sync_status ENUM('active', 'paused', 'error') DEFAULT 'active' COMMENT '同步状态',
    error_count INT DEFAULT 0 COMMENT '错误计数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_sync_enabled (sync_enabled),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time),
    
    -- 外键约束
    FOREIGN KEY fk_sync_user (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信同步配置表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('wechat_webhook_enabled', 'true', 'boolean', '微信Webhook是否启用'),
('max_sync_attempts', '3', 'number', '最大同步重试次数'),
('token_expire_minutes', '10', 'number', '绑定Token过期时间（分钟）'),
('sync_batch_size', '50', 'number', '批量同步消息数量'),
('webhook_verify_signature', 'true', 'boolean', '是否验证Webhook签名'),
('media_id_expire_hours', '72', 'number', '媒体文件ID有效期（小时）')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- 创建视图：活跃绑定统计
CREATE OR REPLACE VIEW v_active_bindings AS
SELECT 
    DATE(created_at) as binding_date,
    COUNT(*) as daily_bindings,
    COUNT(CASE WHEN binding_status = 'active' THEN 1 END) as active_bindings,
    COUNT(CASE WHEN binding_status = 'pending' THEN 1 END) as pending_bindings
FROM wechat_bindings 
WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY binding_date DESC;

-- 创建视图：消息同步统计
CREATE OR REPLACE VIEW v_sync_statistics AS
SELECT 
    DATE(created_at) as sync_date,
    message_type,
    COUNT(*) as total_messages,
    COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced_messages,
    COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed_messages,
    AVG(sync_attempts) as avg_attempts
FROM wechat_message_logs 
WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY DATE(created_at), message_type
ORDER BY sync_date DESC, message_type;

-- access_token缓存表（云函数环境优化）
CREATE TABLE IF NOT EXISTS access_token_cache (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    corp_id VARCHAR(255) NOT NULL UNIQUE COMMENT '企业ID',
    access_token TEXT NOT NULL COMMENT '访问令牌',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    is_refreshing TINYINT(1) DEFAULT 0 COMMENT '是否正在刷新（并发控制）',
    refresh_started_at DATETIME NULL COMMENT '刷新开始时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_corp_id (corp_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_corp_refreshing (corp_id, is_refreshing)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='access_token缓存表（云函数环境优化）';

-- 客服链接缓存表
CREATE TABLE IF NOT EXISTS kf_link_cache (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    open_kfid VARCHAR(255) NOT NULL COMMENT '客服账号ID',
    scene VARCHAR(64) NOT NULL COMMENT '场景值',
    base_url TEXT NOT NULL COMMENT '客服链接',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引和约束
    UNIQUE KEY unique_kf_scene (open_kfid, scene),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服链接缓存表';

-- API调用统计表
CREATE TABLE IF NOT EXISTS token_api_calls (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    corp_id VARCHAR(255) NOT NULL COMMENT '企业ID',
    call_date DATE NOT NULL COMMENT '调用日期',
    call_count INT DEFAULT 1 COMMENT '调用次数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引和约束
    UNIQUE KEY unique_corp_date (corp_id, call_date),
    INDEX idx_call_date (call_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表';

-- 客服同步游标表
CREATE TABLE IF NOT EXISTS kf_sync_cursors (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    open_kfid VARCHAR(255) NOT NULL UNIQUE COMMENT '客服账号ID',
    last_cursor VARCHAR(255) COMMENT '最后同步游标',
    last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_open_kfid (open_kfid),
    INDEX idx_last_sync_at (last_sync_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服同步游标表';

-- 短期访问令牌表
CREATE TABLE IF NOT EXISTS short_term_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    token VARCHAR(255) NOT NULL UNIQUE COMMENT '短期令牌',
    access_token TEXT NOT NULL COMMENT '企业微信访问令牌',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    -- 索引
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短期访问令牌表';

-- 注意：存储过程在云函数环境中可能有兼容性问题，改为应用层实现
-- 清理过期数据的逻辑将通过应用层的定时任务实现

COMMIT;