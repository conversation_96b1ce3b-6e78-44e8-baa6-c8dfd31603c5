-- 微信转发服务数据库初始化脚本
-- 版本：v1.0 (合并所有迁移)
-- 适用于腾讯云MySQL 8.0
-- 包含所有功能：基础表结构 + 文件元数据增强 + 未绑定用户支持

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS gongzhimall_wechat 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE gongzhimall_wechat;

-- ==================== 核心业务表 ====================

-- 用户注册表（设备注册时创建，独立于微信绑定）
CREATE TABLE IF NOT EXISTS app_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    first_device_id VARCHAR(128) COMMENT '首次注册的设备ID',
    device_count INT DEFAULT 0 COMMENT '设备数量',
    is_wechat_bound BOOLEAN DEFAULT FALSE COMMENT '是否已绑定微信',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_is_wechat_bound (is_wechat_bound),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用用户表';

-- 微信绑定表（支持未绑定用户）
CREATE TABLE IF NOT EXISTS wechat_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NULL COMMENT '企业微信外部用户ID（未绑定时为NULL）',
    binding_status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '绑定状态',
    binding_token VARCHAR(255) NULL COMMENT '绑定Token',
    token_expires_at TIMESTAMP NULL COMMENT 'Token过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    UNIQUE KEY uk_external_userid_not_null (external_userid),
    INDEX idx_binding_status (binding_status),
    INDEX idx_binding_token (binding_token),
    INDEX idx_user_binding_status (user_uuid, binding_status),
    INDEX idx_created_at (created_at),

    -- 外键约束
    FOREIGN KEY fk_wechat_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信绑定关系表（支持未绑定用户）';

-- 用户设备绑定表（支持跨端增量同步）
CREATE TABLE IF NOT EXISTS user_device_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    device_id VARCHAR(128) NOT NULL COMMENT '设备唯一标识',
    device_name VARCHAR(255) COMMENT '设备名称',
    platform VARCHAR(50) NOT NULL COMMENT '设备平台（ios, android, desktop, web, harmonyos, etc.）',
    platform_version VARCHAR(50) COMMENT '平台版本号',
    app_version VARCHAR(50) COMMENT '应用版本',
    device_model VARCHAR(100) COMMENT '设备型号',
    last_synced_id BIGINT DEFAULT 0 COMMENT '该设备已同步的最新消息ID',
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    device_status ENUM('active', 'inactive', 'blocked') DEFAULT 'active' COMMENT '设备状态',
    push_token VARCHAR(255) COMMENT 'JPush推送Token',
    push_provider VARCHAR(50) DEFAULT 'jpush' COMMENT '推送服务提供商（jpush, apns, fcm, hms等）',
    device_fingerprint VARCHAR(255) COMMENT '设备指纹（用于安全验证）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_device (user_uuid, device_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_device_id (device_id),
    INDEX idx_platform (platform),
    INDEX idx_last_synced_id (last_synced_id),
    INDEX idx_last_active_at (last_active_at),
    INDEX idx_device_status (device_status),
    INDEX idx_push_provider (push_provider),

    -- 外键约束
    FOREIGN KEY fk_device_user (user_uuid) REFERENCES app_users(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设备绑定表（支持跨端增量同步）';

-- 微信消息索引表（完整版本，包含所有字段）
CREATE TABLE IF NOT EXISTS wechat_message_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    external_userid VARCHAR(128) NOT NULL COMMENT '企业微信外部用户ID',
    wechat_message_id VARCHAR(128) NOT NULL COMMENT '微信消息ID（用于去重）',
    message_type ENUM('text', 'image', 'voice', 'video', 'file', 'location', 'link', 'event', 'unknown') NOT NULL COMMENT '消息类型',
    
    -- 文本消息内容
    content TEXT NULL COMMENT '文本消息内容',
    
    -- 文件缓存机制
    file_name VARCHAR(500) NULL COMMENT '文件名称',
    file_path VARCHAR(1000) NULL COMMENT '加密文件在服务器上的存储路径',
    file_size BIGINT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(200) NULL COMMENT '文件MIME类型',
    
    -- 文件格式识别字段
    file_extension VARCHAR(20) NULL COMMENT '文件扩展名（如jpg、pdf、docx）',
    mime_type VARCHAR(200) NULL COMMENT '检测到的MIME类型',
    original_mime_type VARCHAR(200) NULL COMMENT '原始MIME类型（用于对比）',
    file_magic_detected BOOLEAN DEFAULT FALSE COMMENT '是否通过文件魔数检测到类型',
    media_id VARCHAR(255) NULL COMMENT '企业微信媒体ID',
    media_id_expires_at TIMESTAMP NULL COMMENT '媒体ID过期时间（72小时）',
    
    -- 位置消息字段
    location_x DECIMAL(10, 6) NULL COMMENT '位置纬度',
    location_y DECIMAL(10, 6) NULL COMMENT '位置经度',
    location_scale INT NULL COMMENT '地图缩放级别',
    location_label VARCHAR(500) NULL COMMENT '位置标签描述',
    
    -- 链接消息字段
    link_title VARCHAR(500) NULL COMMENT '链接标题',
    link_description TEXT NULL COMMENT '链接描述',
    link_url TEXT NULL COMMENT '链接地址',
    link_pic_url TEXT NULL COMMENT '链接缩略图地址',
    
    -- 事件消息字段
    event_type VARCHAR(100) NULL COMMENT '事件类型',
    event_key VARCHAR(255) NULL COMMENT '事件键值',
    event_data JSON NULL COMMENT '事件数据',
    
    -- 下载管理
    download_token TEXT NULL COMMENT '下载令牌（JWT）',
    download_expires_at TIMESTAMP NULL COMMENT '下载链接过期时间（24小时）',
    file_expires_at TIMESTAMP NULL COMMENT '文件过期时间（3天后删除）',
    downloaded BOOLEAN DEFAULT FALSE COMMENT '是否已被用户下载（阅后即焚标记）',
    downloaded_at TIMESTAMP NULL COMMENT '下载时间',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    last_download_at TIMESTAMP NULL COMMENT '最后下载时间',
    download_user_agent TEXT NULL COMMENT '下载时的User-Agent',
    
    -- 文件处理状态
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '文件处理状态',
    processing_error TEXT NULL COMMENT '处理错误信息',
    file_hash VARCHAR(64) NULL COMMENT '文件SHA256哈希值（用于去重）',
    
    -- 同步状态
    metadata JSON COMMENT '消息元数据（文件名、大小等非隐私信息）',
    sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending' COMMENT '同步状态',
    sync_attempts INT DEFAULT 0 COMMENT '同步尝试次数',
    last_sync_at TIMESTAMP NULL COMMENT '最后同步时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_wechat_message_id (wechat_message_id),
    INDEX idx_message_type (message_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_download_expires (download_expires_at),
    INDEX idx_file_expires (file_expires_at),
    INDEX idx_downloaded (downloaded),
    INDEX idx_created_at (created_at),
    
    -- 文件类型相关索引
    INDEX idx_file_extension (file_extension),
    INDEX idx_mime_type (mime_type),
    INDEX idx_media_id (media_id),
    INDEX idx_media_id_expires (media_id_expires_at),
    INDEX idx_location_coords (location_x, location_y),
    INDEX idx_event_type (event_type),
    INDEX idx_processing_status (processing_status),
    INDEX idx_file_hash (file_hash),
    INDEX idx_download_count (download_count),
    
    -- 唯一约束防止消息重复
    UNIQUE KEY uk_wechat_message_id (wechat_message_id),
    
    -- 外键约束
    FOREIGN KEY fk_binding (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信消息索引表（完整版本，支持所有消息类型）';

-- ==================== 系统管理表 ====================

-- 系统日志表（适配轻量服务器环境，使用简单的年份分区）
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
    log_level ENUM('ERROR', 'WARN', 'INFO', 'DEBUG') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志消息',
    module VARCHAR(100) COMMENT '模块名称',
    function_name VARCHAR(100) COMMENT '服务名称',
    request_id VARCHAR(100) COMMENT '请求ID（服务器RequestId）',
    user_uuid VARCHAR(36) COMMENT '用户UUID',
    external_userid VARCHAR(128) COMMENT '企业微信用户ID',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_stack TEXT COMMENT '错误堆栈',
    metadata JSON COMMENT '额外的元数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT 'User-Agent',
    execution_time INT COMMENT '执行时间（毫秒）',
    memory_usage INT COMMENT '内存使用量（MB）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    log_year INT NOT NULL DEFAULT (YEAR(CURRENT_TIMESTAMP)) COMMENT '日志年份（用于分区）',
    
    -- 复合主键，包含分区字段
    PRIMARY KEY (id, log_year),
    
    -- 索引
    INDEX idx_log_level (log_level),
    INDEX idx_module (module),
    INDEX idx_function_name (function_name),
    INDEX idx_request_id (request_id),
    INDEX idx_user_uuid (user_uuid),
    INDEX idx_external_userid (external_userid),
    INDEX idx_error_code (error_code),
    INDEX idx_created_at (created_at),
    INDEX idx_log_year (log_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表（适配轻量服务器环境）';

-- 同步配置表
CREATE TABLE IF NOT EXISTS wechat_sync_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_uuid VARCHAR(36) NOT NULL COMMENT '用户UUID',
    sync_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用同步',
    sync_types JSON COMMENT '同步的消息类型配置',
    sync_frequency INT DEFAULT 300 COMMENT '同步频率（秒）',
    last_sync_time TIMESTAMP NULL COMMENT '最后同步时间',
    sync_status ENUM('active', 'paused', 'error') DEFAULT 'active' COMMENT '同步状态',
    error_count INT DEFAULT 0 COMMENT '错误计数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_user_uuid (user_uuid),
    INDEX idx_sync_enabled (sync_enabled),
    INDEX idx_sync_status (sync_status),
    INDEX idx_last_sync_time (last_sync_time),
    
    -- 外键约束
    FOREIGN KEY fk_sync_user (user_uuid) REFERENCES wechat_bindings(user_uuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信同步配置表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
