# 公职猫微信转发服务 - 自动化部署完整指南

## 🎯 部署系统概述

本自动化部署系统已经完成开发并通过测试验证（93%成功率），为公职猫微信转发服务提供了完整的一键部署解决方案。

### ✨ 核心特性

- **🚀 一键部署**: 完全自动化的部署流程
- **🔐 安全管理**: 环境变量加密存储和传输
- **📊 实时监控**: 服务健康检查和性能监控
- **🔄 自动回滚**: 检测异常时自动回滚到稳定版本
- **🌍 多环境支持**: 生产、测试环境独立管理
- **📈 可视化仪表板**: HTML监控仪表板

## 🚀 快速开始

### 第一次部署

```bash
# 1. 进入部署目录
cd backend/wechat/deploy

# 2. 运行一键部署
./one-click-deploy.sh
```

### 后续部署

```bash
# 部署到生产环境
node deploy.js deploy production

# 部署到测试环境
node deploy.js deploy staging
```

## 📋 部署前准备

### 1. 服务器要求

- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+
- **Node.js**: 18.0+ 版本
- **内存**: 最低 1GB，推荐 2GB+
- **磁盘**: 最低 10GB 可用空间
- **网络**: 稳定的互联网连接

### 2. 服务器配置

```bash
# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2
npm install -g pm2

# 安装宝塔面板（可选）
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 3. SSH密钥配置

```bash
# 生成SSH密钥（如果没有）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 复制公钥到服务器
ssh-copy-id <EMAIL>

# 测试连接
ssh <EMAIL> "echo 'SSH连接成功'"
```

## ⚙️ 环境配置

### 方式一：交互式配置

```bash
# 运行环境配置脚本
./setup-env.sh
```

### 方式二：使用加密保险库

```bash
# 1. 生成示例配置
node env-vault.js sample

# 2. 编辑 .env.example 文件
vim .env.example

# 3. 加密环境变量
node env-vault.js encrypt .env.example

# 4. 部署到服务器
node env-vault.js deploy production
```

### 方式三：手动配置

```bash
# 复制模板文件
cp config/env.template .env

# 编辑环境变量
vim .env
```

## 🔧 部署配置

### 修改部署配置

编辑 `deploy-config.json` 文件：

```json
{
  "environments": {
    "production": {
      "server": {
        "host": "your-server.com",
        "user": "root",
        "deployPath": "/www/wwwroot/wechat-service"
      }
    }
  }
}
```

### PM2配置

编辑 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'gongzhimall-wechat',
    script: 'index.js',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

## 🚀 执行部署

### 完整部署流程

```bash
# 1. 测试部署系统
./test-deployment.sh

# 2. 执行一键部署
./one-click-deploy.sh

# 3. 验证部署结果
node deploy.js health production
```

### 高级部署选项

```bash
# 仅部署代码（不重启服务）
node deploy.js deploy production --no-restart

# 强制重新安装依赖
node deploy.js deploy production --force-install

# 跳过备份
node deploy.js deploy production --no-backup
```

## 📊 监控和维护

### 健康检查

```bash
# 检查服务健康状态
node monitor.js health production

# 检查服务状态
node monitor.js status production

# 性能检查
node monitor.js performance production
```

### 生成监控报告

```bash
# 生成监控报告
node monitor.js report production

# 生成HTML仪表板
node monitor.js dashboard production
```

### 持续监控

```bash
# 启动持续监控（每30秒检查一次）
node monitor.js watch production 30000
```

## 🔄 故障处理

### 自动回滚

```bash
# 检查是否需要回滚
./auto-rollback.sh production

# 手动回滚
node deploy.js rollback production
```

### 常见问题解决

#### 1. 部署失败

```bash
# 检查服务器连接
ssh <EMAIL> "echo 'test'"

# 检查磁盘空间
ssh <EMAIL> "df -h"

# 检查服务日志
ssh <EMAIL> "pm2 logs gongzhimall-wechat"
```

#### 2. 服务启动失败

```bash
# 检查端口占用
ssh <EMAIL> "netstat -tlnp | grep :3000"

# 检查环境变量
ssh <EMAIL> "cd /www/wwwroot/wechat.api.gongzhimall.com && cat .env"

# 手动启动服务
ssh <EMAIL> "cd /www/wwwroot/wechat.api.gongzhimall.com && node index.js"
```

#### 3. 健康检查失败

```bash
# 检查服务响应
curl -f https://wechat.api.gongzhimall.com/health

# 检查SSL证书
curl -I https://wechat.api.gongzhimall.com

# 检查防火墙
ssh <EMAIL> "iptables -L"
```

## 📈 性能优化

### 服务器优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化

```bash
# 启用PM2集群模式
pm2 start ecosystem.config.js --env production -i max

# 配置日志轮转
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## 🔐 安全建议

### 1. 服务器安全

- 定期更新系统和软件包
- 配置防火墙规则
- 使用SSH密钥认证
- 禁用root密码登录

### 2. 应用安全

- 定期更新依赖包
- 使用强密码和密钥
- 启用HTTPS
- 配置安全头

### 3. 数据安全

- 定期备份数据库
- 加密敏感配置
- 限制数据库访问
- 监控异常活动

## 📞 技术支持

### 获取帮助

```bash
# 查看部署脚本帮助
./one-click-deploy.sh --help

# 查看监控工具帮助
node monitor.js help

# 查看环境变量工具帮助
node env-vault.js help
```

### 联系方式

- **技术文档**: [项目README](../README.md)
- **API文档**: [API接口文档](../docs/API_DOCUMENTATION.md)
- **问题反馈**: 通过项目Issue系统

## 📝 更新日志

### v1.0.0 (2025-01-23)

- ✅ 完成自动化部署系统开发
- ✅ 实现环境变量加密管理
- ✅ 添加监控和健康检查
- ✅ 支持自动回滚功能
- ✅ 通过93%的系统测试

---

**注意**: 请在生产环境部署前仔细阅读本指南，并在测试环境中验证所有功能。
