{"project": {"name": "公职猫微信转发服务", "version": "1.0.0", "description": "企业微信消息转发服务，支持文件代理下载和推送通知"}, "environments": {"production": {"server": {"host": "wechat.api.gongzhimall.com", "user": "root", "port": 22, "deployPath": "/www/wwwroot/wechat.api.gongzhimall.com", "backupPath": "/www/backup/wechat-service", "logPath": "/var/log/wechat-service"}, "service": {"name": "gongzhimall-wechat", "port": 3000, "nodeVersion": "18", "processManager": "pm2", "healthCheckUrl": "/health", "startupScript": "index.js"}, "database": {"host": "**********", "port": 3306, "name": "gongzhimall_wechat", "user": "wechat_user"}, "ssl": {"enabled": true, "domain": "wechat.api.gongzhimall.com", "autoRenew": true}}, "staging": {"server": {"host": "staging.wechat.api.gongzhimall.com", "user": "root", "port": 22, "deployPath": "/www/wwwroot/staging-wechat", "backupPath": "/www/backup/staging-wechat", "logPath": "/var/log/staging-wechat"}, "service": {"name": "gongzhimall-wechat-staging", "port": 3001, "nodeVersion": "18", "processManager": "pm2", "healthCheckUrl": "/health", "startupScript": "index.js"}, "database": {"host": "**********", "port": 3306, "name": "gongzhimall_wechat_staging", "user": "wechat_user_staging"}, "ssl": {"enabled": false, "domain": "staging.wechat.api.gongzhimall.com", "autoRenew": false}}}, "deployment": {"strategy": "rolling", "maxDowntime": "30s", "healthCheckTimeout": 60, "healthCheckRetries": 30, "backupRetention": 5, "excludePatterns": ["node_modules/", ".git/", ".env", "*.log", "cache/", "logs/", "*.tmp", ".DS_Store", "deploy/", "docs/", "*.md", ".giti<PERSON>re", "package-lock.json"], "preDeployHooks": ["check_dependencies", "validate_config", "create_backup"], "postDeployHooks": ["install_dependencies", "setup_environment", "start_service", "health_check", "notify_completion"]}, "monitoring": {"healthCheck": {"enabled": true, "interval": 30, "timeout": 10, "retries": 3, "endpoints": ["/health", "/api/status"]}, "logging": {"level": "info", "rotation": {"maxSize": "10MB", "maxFiles": 5}, "destinations": ["file", "console"]}, "alerts": {"enabled": true, "channels": ["email", "webhook"], "thresholds": {"responseTime": 5000, "errorRate": 0.05, "memoryUsage": 0.8, "diskUsage": 0.9}}}, "security": {"firewall": {"allowedPorts": [22, 80, 443, 3000], "allowedIPs": ["0.0.0.0/0"], "rateLimiting": {"enabled": true, "requests": 100, "window": "15m"}}, "ssl": {"minVersion": "TLSv1.2", "ciphers": "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS", "hsts": {"enabled": true, "maxAge": 31536000, "includeSubDomains": true}}, "headers": {"xFrameOptions": "DENY", "xContentTypeOptions": "nosniff", "xXssProtection": "1; mode=block", "referrerPolicy": "strict-origin-when-cross-origin"}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": {"daily": 7, "weekly": 4, "monthly": 12}, "compression": true, "encryption": false, "destinations": ["local", "cloud"]}, "rollback": {"enabled": true, "strategy": "automatic", "triggers": ["health_check_failure", "high_error_rate", "manual"], "maxRollbacks": 3, "cooldownPeriod": "5m"}}