# 公职猫微信转发服务 API 接口文档

## 📋 概述

本文档详细描述了公职猫微信转发服务的所有API接口，包括企业微信消息处理、用户绑定管理、消息同步等功能。

**服务地址**: 腾讯云轻量应用服务器  
**认证方式**: 签名验证 (除企业微信Webhook外)  
**数据格式**: JSON  
**字符编码**: UTF-8

## 🏗️ 架构设计说明

### 临时缓存架构 (Temporary Cache Architecture)
**🔐 核心原则**：本服务采用**服务器代理下载 + 临时加密缓存**的架构设计，严格遵循"U盘级私密"的设计理念。

#### 1. **服务端职责**（代理下载与临时缓存）
- ✅ **存储消息元数据**：消息ID、类型、时间戳、发送方标识
- ✅ **代理文件下载**：从企业微信API下载文件并加密存储在服务器本地
- ✅ **提供下载服务**：为客户端提供安全的文件下载接口
- ❌ **不长期存储文件**：文件下载后或3天后自动删除（阅后即焚）
- ❌ **不存储明文内容**：所有文件均以加密形式临时存储

#### 2. **客户端职责**（从我方服务器获取数据）
- 通过 `/api/sync/messages` 获取消息元数据列表和下载链接
- 通过 `/api/media/download/:token` 从我方服务器下载文件
- **无需直接调用企业微信API**：所有数据均通过我方服务器中转

#### 3. **数据流向图**
```
企业微信 → 本服务(下载并加密缓存) → 客户端获取元数据和下载链接 → 客户端从我方服务器下载文件
```

#### 4. **架构优势**
- 🔐 **绝对隐私保护**：文件在服务器端加密存储，阅后即焚
- 💰 **成本可控**：临时缓存策略，无长期存储成本
- ⚡ **性能稳定**：轻量服务器处理大文件，无执行时长限制
- 🛡️ **安全性最高**：一次性下载令牌，防止文件泄露
- 📏 **合规性强**：符合《个人信息保护法》等法规要求

#### 5. **重要声明**
**本API服务提供文件代理下载服务**，所有媒体文件由服务器从企业微信API下载后加密存储，客户端通过一次性下载令牌从我方服务器获取文件。文件在用户下载后或3天后自动删除。

## 🔐 认证说明

### 签名验证
除企业微信Webhook接口外，所有API都需要进行签名验证：

**请求头参数**:
- `X-Token`: 访问令牌
- `X-Timestamp`: 时间戳（毫秒）
- `X-Signature`: 签名值

**签名算法**:
```javascript
const signature = crypto.createHmac('sha256', TOKEN_SECRET)
  .update(JSON.stringify(requestBody) + timestamp + token)
  .digest('hex');
```

## 📡 API 接口列表

### 0. 系统监控接口

#### 0.1 健康检查
```
GET /health
```

**描述**: 检查服务器运行状态和各组件健康状况

**响应**:
```json
{
  "status": "ok",
  "timestamp": "2025-01-15T10:30:00Z",
  "checks": {
    "database": "ok",
    "storage": "ok",
    "memory": "128MB"
  },
  "cache": {
    "total_files": 42,
    "total_size": "156.7MB",
    "expired_files": 3
  }
}
```

**错误响应**:
```json
{
  "status": "error",
  "timestamp": "2025-01-15T10:30:00Z",
  "checks": {
    "database": "error",
    "storage": "unknown",
    "memory": "128MB"
  }
}
```

### 1. 企业微信Webhook处理

#### 1.1 URL验证 (开启接收)
```
GET /api/wechat/webhook
```

**描述**: 企业微信配置Webhook时的URL验证接口

**请求参数**:
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| msg_signature | string | 是 | 消息签名 |
| timestamp | string | 是 | 时间戳 |
| nonce | string | 是 | 随机数 |
| echostr | string | 是 | 回显字符串 |

**响应**:
```
直接返回解密后的echostr明文（无JSON格式）
```

**示例**:
```bash
GET /api/wechat/webhook?msg_signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx
```

#### 1.2 消息处理 (使用接收)
```
POST /api/wechat/webhook
```

**描述**: 接收企业微信推送的消息

**请求体**:
```json
{
  "ToUserName": "企业微信CorpID",
  "FromUserName": "发送方UserID", 
  "CreateTime": 1234567890,
  "MsgType": "text|image|voice|video|file|event",
  "Encrypt": "加密的消息内容"
}
```

**响应**:
```json
{
  "success": true,
  "message": "消息处理成功"
}
```

### 2. 用户绑定管理

#### 2.1 查询绑定状态
```
GET /api/bind/status
```

**描述**: 根据用户UUID查询微信绑定状态

**请求参数**:
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_uuid | string | 是 | 用户唯一标识 |

**响应**:

**绑定成功时**:
```json
{
  "success": true,
  "bound": true,
  "status": "success",
  "data": {
    "user_uuid": "用户UUID",
    "external_userid": "企业微信外部用户ID",
    "binding_status": "active|inactive",
    "binding_time": "2025-01-15T10:30:00Z"
  }
}
```

**未绑定时**:
```json
{
  "success": false,
  "bound": false,
  "message": "用户未绑定微信"
}
```

**示例**:
```bash
GET /api/bind/status?user_uuid=12345678-1234-1234-1234-123456789012
```

#### 2.3 解除用户绑定
```
DELETE /api/bind/unbind
```

**描述**: 解除用户的微信绑定关系

**请求参数**:
```json
{
  "user_uuid": "用户唯一标识"
}
```

**响应**:

**解绑成功时**:
```json
{
  "success": true,
  "message": "解除绑定成功"
}
```

**用户未绑定时**:
```json
{
  "success": false,
  "message": "用户未绑定微信账号",
  "not_bound": true
}
```

**示例**:
```bash
DELETE /api/bind/unbind
Content-Type: application/json

{
  "user_uuid": "12345678-1234-1234-1234-123456789012"
}
```

#### 2.2 生成一键绑定链接
```
POST /api/bind/one-click-link
```

**描述**: 为用户生成一键绑定链接，用于微信客服绑定流程

**请求参数**:
```json
{
  "user_uuid": "用户唯一标识"
}
```

**响应**:
```json
{
  "success": true,
  "binding_url": "https://work.weixin.qq.com/kf/kfid?scene_param=encrypted_token",
  "expires_at": "2025-01-16T10:30:00Z"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "用户已经绑定过微信账号了",
  "already_bound": true
}
```

### 3. 设备管理接口

#### 3.1 注册设备
```
POST /api/sync/register-device
```

**描述**: 注册用户设备，用于推送通知和消息同步

**请求参数**:
```json
{
  "user_uuid": "用户唯一标识",
  "device_id": "设备唯一标识",
  "push_token": "推送令牌",
  "push_provider": "jpush|apns|fcm|hms",
  "device_info": {
    "platform": "ios|android",
    "app_version": "1.0.0",
    "os_version": "iOS 17.0"
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "设备注册成功"
}
```

### 4. 消息同步接口

#### 4.1 增量同步消息
```
GET /api/sync/messages
```

**描述**: 获取用户的增量消息列表

**🔐 临时缓存架构说明**：
- 本接口返回消息**元数据**和**下载链接**
- **各类型消息的内容获取方式**：
  - `text`: 文本消息内容直接包含在响应中
  - `image`: 图片文件通过`download_url`从我方服务器下载
  - `voice`: 语音文件通过`download_url`从我方服务器下载
  - `video`: 视频文件通过`download_url`从我方服务器下载
  - `file`: 文档文件通过`download_url`从我方服务器下载
  - `event`: 事件消息的详细信息包含在响应中
- **统一下载流程**：
  1. 通过本接口获取消息列表和下载链接
  2. 使用 `download_url` 中的令牌调用 `/api/media/download/:token` 下载文件
- **服务器负责文件的代理下载和安全中转**

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| user_uuid | string | 是 | - | 用户唯一标识 |
| device_id | string | 是 | - | 设备唯一标识 |
| since_id | number | 否 | 0 | 起始消息ID |
| limit | number | 否 | 100 | 返回数量限制 |

**响应**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 123,
        "message_type": "text|image|voice|video|file|unknown:xxx",
        "wechat_message_id": "企业微信消息ID",
        "content": "文本消息内容（仅text类型）",
        "download_url": "/api/media/download/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...（仅媒体类型）",
        "download_expires_at": "2025-01-16T10:30:00Z",
        "metadata": {
          "timestamp": 1234567890,
          "from_user": "发送方",
          "to_user": "接收方",
          "file_name": "文件名（如有）",
          "file_size": 1024
        },
        "created_at": "2025-01-15T10:30:00Z"
      }
    ],
    "has_more": true,
    "next_since_id": 124
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "error": "缺少用户或设备标识",
  "details": {
    "user_uuid": "valid|missing",
    "device_id": "valid|missing"
  }
}
```

**示例**:
```bash
GET /api/sync/messages?user_uuid=12345678-1234-1234-1234-123456789012&device_id=device123&since_id=100&limit=50
```

#### 4.2 确认消息同步
```
POST /api/sync/ack
```

**描述**: 确认客户端已成功同步到某条消息，并更新服务端记录的设备同步状态。

**请求体**:
```json
{
  "user_uuid": "用户唯一标识",
  "device_id": "设备唯一标识", 
  "last_synced_id": 123
}
```

**响应**:
```json
{
  "success": true,
  "message": "同步状态更新成功"
}
```

**错误响应**:
```json
{
  "success": false,
  "error": "参数不完整",
  "details": {
    "user_uuid": "valid|missing",
    "device_id": "valid|missing",
    "last_synced_id": "valid|missing"
  }
}
```


```

**响应**:
```json
{
  "success": true,
  "message": "设备注册成功",
  "data": {
    "device_id": "设备ID",
    "registered_at": "2025-01-15T10:30:00Z"
  }
}
```

#### 3.4 文件下载接口
```
GET /api/media/download/:token
```

**描述**: 通过一次性令牌下载媒体文件

**🔑 使用说明**：
- 此接口使用一次性下载令牌，确保文件下载安全
- 令牌有效期为24小时，过期后自动重新生成（用户无感知）
- 文件下载后会立即从服务器删除（阅后即焚）
- 支持断点续传和流式下载
- 如果用户24小时内未下载，重新请求消息列表时会自动获得新的下载链接

**请求参数**:
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| token | string | 是 | 一次性下载令牌（URL路径参数） |

**请求头**:
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| Range | string | 否 | 支持断点续传 (如: bytes=0-1023) |

**响应**:
- **成功**: 直接返回文件流
- **失败**: 返回JSON错误信息

**响应头**:
```
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="filename.ext"
Content-Length: 1024
Accept-Ranges: bytes
```

**错误响应**:
```json
{
  "success": false,
  "error": "令牌无效或已过期",
  "code": "INVALID_TOKEN"
}
```

**示例**:
```bash
GET /api/media/download/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 📊 数据结构说明

### 消息类型 (message_type)
- `text`: 文本消息
- `image`: 图片消息
- `voice`: 语音消息
- `video`: 视频消息
- `file`: 文件消息
- `event`: 事件消息
- `unknown:xxx`: 未知类型消息（xxx为原始类型）

### 绑定状态 (binding_status)
- `active`: 已绑定且活跃
- `inactive`: 已绑定但非活跃
- `pending`: 绑定中
- `expired`: 绑定已过期

### 平台类型 (platform)
- `ios`: iOS设备
- `android`: Android设备
- `desktop`: 桌面端设备

## 🔧 错误码说明

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| MISSING_AUTH | 401 | 缺少认证信息 |
| INVALID_SIGNATURE | 401 | 签名验证失败 |
| TIMESTAMP_EXPIRED | 401 | 请求时间戳过期 |
| VALIDATION_ERROR | 400 | 参数验证失败 |
| USER_NOT_FOUND | 404 | 用户不存在 |
| DEVICE_NOT_FOUND | 404 | 设备不存在 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 🚀 使用示例

### JavaScript/Node.js 示例

```javascript
const crypto = require('crypto');

// 签名生成函数
function generateSignature(body, timestamp, token, secret) {
  const data = JSON.stringify(body) + timestamp + token;
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
}

// API调用示例
async function syncMessages(userUuid, deviceId, sinceId = 0) {
  const timestamp = Date.now().toString();
  const token = 'your-access-token';
  const secret = 'your-token-secret';
  
  const queryParams = { user_uuid: userUuid, device_id: deviceId, since_id: sinceId };
  const signature = generateSignature(queryParams, timestamp, token, secret);
  
  const response = await fetch('/api/sync/messages?' + new URLSearchParams(queryParams), {
    method: 'GET',
    headers: {
      'X-Token': token,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'Content-Type': 'application/json'
    }
  });
  
  return await response.json();
}

// 文件下载示例
async function downloadFile(downloadUrl, fileName) {
  try {
    const response = await fetch(downloadUrl);
    
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status}`);
    }
    
    const blob = await response.blob();
    
    // 在浏览器中触发下载
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    return {
      success: true,
      fileName: fileName,
      size: blob.size
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 注册设备示例
async function registerDevice(userUuid, deviceId, platform) {
  const timestamp = Date.now().toString();
  const token = 'your-access-token';
  const secret = 'your-token-secret';
  
  const body = {
    user_uuid: userUuid,
    device_id: deviceId,
    device_name: 'My Device',
    platform: platform,
    app_version: '1.0.0'
  };
  
  const signature = generateSignature(body, timestamp, token, secret);
  
  const response = await fetch('/api/sync/register-device', {
    method: 'POST',
    headers: {
      'X-Token': token,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(body)
  });
  
  return await response.json();
}
```

### React Native 示例

```javascript
import CryptoJS from 'crypto-js';
import RNFS from 'react-native-fs';

class WeChatAPI {
  constructor(baseURL, tokenSecret) {
    this.baseURL = baseURL;
    this.tokenSecret = tokenSecret;
  }
  
  generateSignature(body, timestamp, token) {
    const data = JSON.stringify(body) + timestamp + token;
    return CryptoJS.HmacSHA256(data, this.tokenSecret).toString();
  }
  
  async request(endpoint, options = {}) {
    const timestamp = Date.now().toString();
    const token = 'your-access-token';
    const signature = this.generateSignature(options.body || {}, timestamp, token);
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'X-Token': token,
        'X-Timestamp': timestamp,
        'X-Signature': signature,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    return await response.json();
  }
  
  // 获取消息列表
  async getMessages(userUuid, deviceId, sinceId = 0, limit = 100) {
    const params = new URLSearchParams({
      user_uuid: userUuid,
      device_id: deviceId,
      since_id: sinceId.toString(),
      limit: limit.toString()
    });
    
    return this.request(`/api/sync/messages?${params}`);
  }
  
  // 下载文件
  async downloadFile(downloadUrl, fileName, savePath) {
    try {
      const downloadResult = await RNFS.downloadFile({
        fromUrl: `${this.baseURL}${downloadUrl}`,
        toFile: `${RNFS.DocumentDirectoryPath}/${savePath}/${fileName}`,
        headers: {
          'Accept': 'application/octet-stream'
        }
      }).promise;
      
      if (downloadResult.statusCode === 200) {
        return {
          success: true,
          filePath: `${RNFS.DocumentDirectoryPath}/${savePath}/${fileName}`,
          fileName: fileName
        };
      } else {
        throw new Error(`下载失败: ${downloadResult.statusCode}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  // 更新同步状态
  async ackSync(userUuid, deviceId, lastSyncedId) {
    return this.request('/api/sync/ack', {
      method: 'POST',
      body: JSON.stringify({
        user_uuid: userUuid,
        device_id: deviceId,
        last_synced_id: lastSyncedId
      })
    });
  }
}

// 使用示例
const api = new WeChatAPI('https://your-server-url.com', 'your-token-secret');

// 📋 完整的消息同步工作流示例
async function syncAndProcessMessages(userUuid, deviceId) {
  try {
    // 1. 获取消息元数据列表
    const messagesResponse = await api.getMessages(userUuid, deviceId, 0, 50);
    const { messages } = messagesResponse.data;
    
    // 2. 处理消息并下载文件
    const processedMessages = [];
    for (const message of messages) {
      if (message.message_type === 'text') {
        // 文本消息直接使用
        processedMessages.push({
          ...message,
          processed: true
        });
      } else if (message.download_url) {
        // 媒体文件消息：下载文件
        const fileName = message.metadata.file_name || `${message.message_type}_${message.id}`;
        const downloadResult = await api.downloadFile(message.download_url, fileName, 'downloads');
        
        processedMessages.push({
          ...message,
          localFilePath: downloadResult.filePath,
          downloaded: downloadResult.success,
          processed: true
        });
      }
    }
    
    // 3. 确认同步状态
    if (messages.length > 0) {
      const lastMessageId = messages[messages.length - 1].id;
      await api.ackSync(userUuid, deviceId, lastMessageId);
    }
    
    return processedMessages;
  } catch (error) {
    console.error('消息同步失败:', error);
    return [];
  }
}
```

## 📝 注意事项

1. **时间戳有效期**: 请求时间戳与服务器时间差不能超过5分钟
2. **签名算法**: 使用HMAC-SHA256算法生成签名
3. **字符编码**: 所有字符串均使用UTF-8编码
4. **文件下载**: 下载令牌有效期为24小时，文件下载后立即删除
5. **并发限制**: 建议控制并发请求数量，避免触发限流
6. **错误重试**: 建议实现指数退避的重试机制
7. **数据安全**: 用户文件在服务器端加密存储，阅后即焚，最大化保障隐私安全

## 📞 技术支持

如有技术问题，请联系：
- 邮箱: <EMAIL>
- 文档更新时间: 2025-01-15

---

**版本**: v2.0.0  
**最后更新**: 2025-01-15  
**适用范围**: 公职猫微信转发服务（轻量服务器架构） 