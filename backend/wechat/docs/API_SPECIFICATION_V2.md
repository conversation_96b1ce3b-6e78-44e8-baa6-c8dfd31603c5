# 微信转发功能API接口规范

## 📋 概述

本文档定义了微信转发功能的完整API接口规范，包括请求格式、响应数据结构、错误处理等。重构后的API支持完整的文件类型信息和智能消息预览。

## 🔗 API接口列表

### 1. 微信Webhook接口

#### 1.1 Webhook URL验证
```
GET /api/wechat/webhook
```

**请求参数：**
```javascript
{
  msg_signature: string,  // 消息签名
  timestamp: string,      // 时间戳
  nonce: string,         // 随机数
  echostr: string        // 验证字符串
}
```

**响应：**
```javascript
// 成功时直接返回echostr
"验证字符串"
```

#### 1.2 消息接收
```
POST /api/wechat/webhook
```

**请求头：**
```
Content-Type: application/xml
```

**请求体：**
```xml
<xml>
  <ToUserName><![CDATA[企业微信CorpID]]></ToUserName>
  <FromUserName><![CDATA[外部用户ID]]></FromUserName>
  <CreateTime>时间戳</CreateTime>
  <MsgType><![CDATA[消息类型]]></MsgType>
  <!-- 根据消息类型包含不同字段 -->
</xml>
```

**响应：**
```javascript
{
  "success": true,
  "message": "消息处理成功"
}
```

### 2. 用户绑定接口

#### 2.1 绑定状态查询
```
GET /api/bind/status?user_uuid={user_uuid}
```

**响应：**
```javascript
{
  "success": true,
  "data": {
    "user_uuid": "用户UUID",
    "is_bound": true,
    "external_userid": "企业微信外部用户ID",
    "binding_status": "active",
    "created_at": "2025-01-24T10:00:00Z"
  }
}
```

#### 2.2 生成绑定链接
```
POST /api/bind/generate
```

**请求体：**
```javascript
{
  "user_uuid": "用户UUID"
}
```

**响应：**
```javascript
{
  "success": true,
  "message": "绑定链接生成成功",
  "data": {
    "binding_url": "https://work.weixin.qq.com/kfid/kfce062e9c6b4dc4f49?scene_param=isMYpyvcf857fjuSAtyAeyscDXNNCOekOswmtj8qRZL9vLfg6rFa5j%2FitbYCBdiA",
    "corp_id": "ww857dc7bfe97b085b",
    "expires_in": 3600
  }
}
```

#### 2.3 解除绑定
```
POST /api/bind/unbind
```

**请求体：**
```javascript
{
  "user_uuid": "用户UUID"
}
```

**响应：**
```javascript
{
  "success": true,
  "message": "绑定已解除"
}
```

### 3. 消息同步接口

#### 3.1 增量消息同步
```
GET /api/sync/messages?user_uuid={user_uuid}&device_id={device_id}&since_id={since_id}&limit={limit}
```

**请求参数：**
- `user_uuid`: 用户UUID（必需）
- `device_id`: 设备ID（必需）
- `since_id`: 起始消息ID，默认0
- `limit`: 限制数量，默认50，最大200

**响应：**
```javascript
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 123,
        "wechat_message_id": "msg_123456",
        "message_type": "file",
        "content": null,
        
        // 文件信息（完整版本）
        "file_name": "report.pdf",
        "file_size": 1024000,
        "content_type": "application/pdf",
        
        // 智能文件类型检测结果
        "file_extension": "pdf",
        "mime_type": "application/pdf",
        "original_mime_type": "application/pdf",
        "file_magic_detected": true,
        
        // 媒体ID信息
        "media_id": "media_123456",
        "media_id_expires_at": "2025-01-27T10:00:00Z",
        
        // 下载信息
        "download_token": "jwt_token_here",
        "download_expires_at": "2025-01-25T10:00:00Z",
        "file_expires_at": "2025-01-27T10:00:00Z",
        "downloaded": false,
        
        // 元数据
        "metadata": {
          "timestamp": 1737705600,
          "from_user": "external_user_123",
          "to_user": "corp_123",
          "file_name": "report.pdf",
          "file_size": 1024000,
          "detected_type": "file"
        },
        
        "created_at": "2025-01-24T10:00:00Z"
      }
    ],
    "has_more": false,
    "next_since_id": 123
  }
}
```

#### 3.2 设备注册
```
POST /api/sync/register
```

**请求体：**
```javascript
{
  "user_uuid": "用户UUID",
  "device_id": "设备唯一标识",
  "device_name": "iPhone 16",
  "platform": "ios",
  "platform_version": "18.0",
  "app_version": "1.0.0",
  "push_token": "JPush推送Token"
}
```

**响应：**
```javascript
{
  "success": true,
  "data": {
    "device_registered": true,
    "last_synced_id": 0
  }
}
```

#### 3.3 同步状态更新
```
POST /api/sync/status
```

**请求体：**
```javascript
{
  "user_uuid": "用户UUID",
  "device_id": "设备ID",
  "last_synced_id": 123
}
```

**响应：**
```javascript
{
  "success": true,
  "message": "同步状态已更新"
}
```

### 4. 媒体文件下载接口

#### 4.1 媒体文件下载
```
GET /api/media/wechat/{media_id}?user_uuid={user_uuid}
```

**请求参数：**
- `media_id`: 企业微信媒体ID（路径参数）
- `user_uuid`: 用户UUID（查询参数，必需）

**响应：**
```
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="filename.ext"
Content-Length: 文件大小

[文件二进制数据]
```

#### 4.2 媒体文件信息
```
GET /api/media/info/{media_id}?user_uuid={user_uuid}
```

**响应：**
```javascript
{
  "success": true,
  "data": {
    "media_id": "media_123456",
    "file_name": "report.pdf",
    "file_size": 1024000,
    "mime_type": "application/pdf",
    "file_extension": "pdf",
    "expires_at": "2025-01-27T10:00:00Z",
    "download_url": "/api/media/wechat/media_123456?user_uuid=xxx"
  }
}
```

## 📊 数据结构规范

### 消息类型枚举
```javascript
enum MessageType {
  TEXT = 'text',        // 文本消息
  IMAGE = 'image',      // 图片消息
  VOICE = 'voice',      // 语音消息
  VIDEO = 'video',      // 视频消息
  FILE = 'file',        // 文件消息
  LOCATION = 'location', // 位置消息
  LINK = 'link',        // 链接消息
  EVENT = 'event',      // 事件消息
  UNKNOWN = 'unknown'   // 未知类型
}
```

### 文件类型检测结果
```javascript
interface FileTypeInfo {
  category: 'image' | 'video' | 'voice' | 'file',  // 文件分类
  extension: string,                                // 文件扩展名
  mimeType: string                                  // MIME类型
}
```

### 智能推送通知数据
```javascript
interface PushNotificationData {
  title: string,           // 推送标题
  content: string,         // 推送内容
  data: {
    type: 'new_message',
    message_type: string,
    message_id: number,
    timestamp: number,
    preview: {
      title: string,
      content: string,
      icon: string,
      category: string
    },
    file_info?: {
      name: string,
      size: number,
      type: string,
      extension: string
    }
  }
}
```

## ⚠️ 错误处理

### 标准错误响应格式
```javascript
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息（可选）"
  }
}
```

### 常见错误码
- `INVALID_SIGNATURE`: 签名验证失败
- `USER_NOT_BOUND`: 用户未绑定微信
- `MEDIA_EXPIRED`: 媒体文件已过期
- `FILE_NOT_FOUND`: 文件不存在
- `DOWNLOAD_LIMIT_EXCEEDED`: 下载次数超限
- `INVALID_PARAMETERS`: 参数错误

## 🔐 安全机制

### 1. 签名验证
所有Webhook请求都需要验证企业微信签名。

### 2. 用户权限验证
所有API请求都需要验证用户UUID的有效性。

### 3. 下载令牌
媒体文件下载使用短期JWT令牌，有效期24小时。

### 4. 访问频率限制
- 消息同步：每分钟最多60次
- 文件下载：每个文件最多下载5次

## 📱 移动端适配

### MessageMetadataAdapter期望的数据格式
移动端适配器期望服务器返回包含以下字段的消息数据：

```javascript
{
  // 基础字段
  "message_type": string,
  "content": string,
  "metadata": object,
  "media_id": string,
  
  // 文件类型字段（重要）
  "mime_type": string,           // 优先使用
  "file_extension": string,      // 文件扩展名
  "original_mime_type": string,  // 原始MIME类型
  
  // 从metadata中提取的字段
  "metadata": {
    "file_name": string,
    "file_size": number,
    "detected_type": string      // 检测到的文件类型
  }
}
```

---

**文档版本**：v1.0  
**最后更新**：2025年1月24日  
**维护者**：Augment Agent
