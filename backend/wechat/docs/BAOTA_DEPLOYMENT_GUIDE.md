# 宝塔面板 + Docker 部署指南

## 📋 概述

本指南详细说明如何使用宝塔面板和Docker部署公职猫微信转发服务。

## 🔧 环境准备

### 1. 服务器要求
- **系统**: CentOS 7+、Ubuntu 18+、Debian 9+
- **内存**: 最少2GB，推荐4GB
- **磁盘**: 至少20GB可用空间
- **网络**: 可访问外网，开放80、443、3000端口

### 2. 宝塔面板安装
```bash
# CentOS安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec

# Ubuntu/Debian安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec
```

### 3. Docker环境配置
在宝塔面板中：
1. 进入 **软件商店** → **运行环境**
2. 安装 **Docker管理器**
3. 安装 **Docker Compose管理器**

## 🚀 部署步骤

### 步骤1：上传项目文件
1. 在宝塔面板 **文件** 管理中创建目录：`/www/wwwroot/gongzhimall-wechat`
2. 上传整个项目文件夹到该目录
3. 设置目录权限为755

### 步骤2：配置环境变量
1. 复制 `config/env.template` 为 `.env`
2. 根据实际情况修改配置：

```bash
# 服务器配置
PORT=3000
SERVER_DOMAIN=your-domain.com

# 数据库配置
MYSQL_HOST=mysql-db  # Docker容器内使用服务名
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=your-secure-password
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信配置
WECHAT_CORP_ID=your-corp-id
WECHAT_CORP_SECRET=your-corp-secret
WECHAT_AGENT_ID=your-agent-id
WECHAT_TOKEN=your-token
WECHAT_ENCODING_AES_KEY=your-aes-key

# 极光推送配置
JPUSH_APP_KEY=your-jpush-key
JPUSH_MASTER_SECRET=your-jpush-secret

# 安全密钥
TOKEN_SECRET=your-token-secret
WECHAT_BINDING_TOKEN_SECRET=your-binding-secret
FILE_ENCRYPTION_KEY=your-file-encryption-key
```

### 步骤3：创建必要目录
在项目根目录下创建：
```bash
mkdir -p cache logs config
chmod 755 cache logs config
```

### 步骤4：Docker部署
在宝塔面板的 **终端** 中：

```bash
# 进入项目目录
cd /www/wwwroot/gongzhimall-wechat

# 构建并启动容器
docker-compose up -d

# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f gongzhimall-wechat
```

### 步骤5：配置Nginx反向代理
在宝塔面板中：

1. **网站** → **添加站点**
   - 域名：`your-domain.com`
   - PHP版本：纯静态
   
2. **设置** → **反向代理**
   - 代理名称：`gongzhimall-wechat`
   - 目标URL：`http://127.0.0.1:3000`
   - 发送域名：`$host`

3. **SSL** → **Let's Encrypt**
   - 申请免费SSL证书

### 步骤6：配置Nginx规则
在反向代理配置中添加：

```nginx
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
}

# 文件下载专用配置
location /api/media/download/ {
    proxy_pass http://127.0.0.1:3000;
    proxy_buffering off;
    proxy_request_buffering off;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    client_max_body_size 100m;
}
```

### 步骤7：配置定时任务
在宝塔面板 **计划任务** 中添加：

```bash
# 每小时清理过期文件
0 * * * * cd /www/wwwroot/gongzhimall-wechat && docker-compose exec gongzhimall-wechat node scripts/cleanup-files.js

# 每天凌晨2点清理数据库
0 2 * * * cd /www/wwwroot/gongzhimall-wechat && docker-compose exec gongzhimall-wechat node scripts/cleanup-database.js

# 每天凌晨3点备份数据库
0 3 * * * cd /www/wwwroot/gongzhimall-wechat && docker-compose exec mysql-db mysqldump -u root -p$MYSQL_ROOT_PASSWORD gongzhimall_wechat > /www/backup/wechat_$(date +\%Y\%m\%d).sql
```

## 🔍 验证部署

### 1. 健康检查
访问：`https://your-domain.com/health`
应该返回：
```json
{
  "status": "ok",
  "timestamp": "2025-01-15T10:30:00Z",
  "checks": {
    "database": "ok",
    "storage": "ok",
    "memory": "512MB"
  }
}
```

### 2. 企业微信Webhook测试
在企业微信管理后台配置Webhook URL：
`https://your-domain.com/api/wechat/webhook`

### 3. 容器状态检查
```bash
# 查看容器运行状态
docker-compose ps

# 查看应用日志
docker-compose logs -f gongzhimall-wechat

# 查看数据库日志
docker-compose logs -f mysql-db
```

## 🔧 维护操作

### 更新应用
```bash
# 进入项目目录
cd /www/wwwroot/gongzhimall-wechat

# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose build --no-cache
docker-compose up -d
```

### 查看日志
```bash
# 实时查看应用日志
docker-compose logs -f gongzhimall-wechat

# 查看错误日志
docker-compose logs --tail=100 gongzhimall-wechat | grep ERROR

# 查看文件缓存状态
docker-compose exec gongzhimall-wechat ls -la /var/www/cache
```

### 数据库管理
```bash
# 进入数据库容器
docker-compose exec mysql-db mysql -u root -p

# 备份数据库
docker-compose exec mysql-db mysqldump -u root -p gongzhimall_wechat > backup.sql

# 恢复数据库
docker-compose exec -T mysql-db mysql -u root -p gongzhimall_wechat < backup.sql
```

### 清理操作
```bash
# 手动清理过期文件
docker-compose exec gongzhimall-wechat node scripts/cleanup-files.js

# 清理Docker镜像
docker system prune -f

# 查看磁盘使用情况
df -h
du -sh /www/wwwroot/gongzhimall-wechat/cache
```

## 🚨 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs gongzhimall-wechat

# 检查端口占用
netstat -tlnp | grep 3000

# 重启容器
docker-compose restart gongzhimall-wechat
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps mysql-db

# 测试数据库连接
docker-compose exec mysql-db mysql -u wechat_user -p -h localhost gongzhimall_wechat
```

#### 3. 文件下载失败
```bash
# 检查缓存目录权限
docker-compose exec gongzhimall-wechat ls -la /var/www/cache

# 检查磁盘空间
df -h

# 手动清理缓存
docker-compose exec gongzhimall-wechat rm -rf /var/www/cache/*
```

#### 4. SSL证书问题
- 在宝塔面板中重新申请SSL证书
- 检查域名DNS解析是否正确
- 确保80端口可以正常访问

## 📊 监控建议

### 1. 宝塔面板监控
- 启用系统监控插件
- 设置CPU、内存、磁盘告警
- 配置服务状态监控

### 2. 应用层监控
```bash
# 创建监控脚本
cat > /www/wwwroot/gongzhimall-wechat/monitor.sh << 'EOF'
#!/bin/bash
HEALTH_URL="https://your-domain.com/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE != "200" ]; then
    echo "Service health check failed: $RESPONSE"
    # 发送告警通知
    # 可以集成企业微信、钉钉等通知方式
fi
EOF

chmod +x monitor.sh
```

### 3. 日志监控
- 定期检查错误日志
- 设置日志轮转
- 配置重要错误的告警

## 📞 技术支持

如遇问题，请联系：
- 邮箱：<EMAIL>
- 提供详细的错误日志和容器状态信息 