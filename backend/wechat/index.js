/**
 * 公职猫微信转发服务 - Express HTTP服务器
 * 腾讯云轻量应用服务器部署版本
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const cron = require('node-cron');
const crypto = require('crypto');

// 导入业务模块
const controller = require('./api/controller');
const { logError } = require('./api/errorHandler');
const { ensureTablesFromSql } = require('./data/database');
const fileService = require('./service/fileService');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// ==================== 中间件配置 ====================

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false, // 允许文件下载
  crossOriginEmbedderPolicy: false
}));

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Token', 'X-Timestamp', 'X-Signature']
}));

// 请求日志
app.use(morgan('combined'));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: '请求过于频繁，请稍后再试'
    }
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 原始XML解析中间件（用于企业微信消息）
app.use('/api/wechat/webhook', (req, res, next) => {
  if (req.method === 'POST' && req.headers['content-type']?.includes('text/xml')) {
    let rawBody = '';
    req.setEncoding('utf8');
    req.on('data', (chunk) => {
      rawBody += chunk;
    });
    req.on('end', () => {
      req.rawBody = rawBody;
      req.body = parseWeChatXML(rawBody);
      next();
    });
  } else {
    next();
  }
});

// ==================== 工具函数 ====================

/**
 * 解析企业微信XML消息体
 */
function parseWeChatXML(xmlString) {
  try {
    const result = {};

    // 提取ToUserName
    const toUserMatch = xmlString.match(/<ToUserName><!\[CDATA\[(.*?)\]\]><\/ToUserName>/);
    if (toUserMatch) result.ToUserName = toUserMatch[1];

    // 提取Encrypt
    const encryptMatch = xmlString.match(/<Encrypt><!\[CDATA\[(.*?)\]\]><\/Encrypt>/);
    if (encryptMatch) result.Encrypt = encryptMatch[1];

    // 提取AgentID
    const agentMatch = xmlString.match(/<AgentID><!\[CDATA\[(.*?)\]\]><\/AgentID>/);
    if (agentMatch) result.AgentID = agentMatch[1];

    console.log('XML解析结果:', {
      ToUserName: result.ToUserName ? result.ToUserName.substring(0, 10) + '...' : 'undefined',
      hasEncrypt: !!result.Encrypt,
      encryptLength: result.Encrypt ? result.Encrypt.length : 0,
      AgentID: result.AgentID
    });

    return result;
  } catch (error) {
    console.error('XML解析失败:', error);
    return {};
  }
}

/**
 * 鉴权中间件
 */
function verifySignature(req, res, next) {
  const TOKEN_SECRET = process.env.TOKEN_SECRET;
  const token = req.headers['x-token'];
  const timestamp = req.headers['x-timestamp'];
  const signature = req.headers['x-signature'];
  const now = Date.now();

  if (!TOKEN_SECRET) {
    return res.status(500).json({
      success: false,
      error: { code: 'MISSING_TOKEN_SECRET', message: '服务器配置错误：缺少TOKEN_SECRET' }
    });
  }

  if (!token || !timestamp || !signature) {
    return res.status(401).json({
      success: false,
      error: { code: 'MISSING_AUTH', message: '缺少鉴权信息' }
    });
  }

  // 自动兼容秒/毫秒级时间戳
  let ts = Number(timestamp);
  if (ts < 1e12) ts = ts * 1000;

  if (Math.abs(now - ts) > 5 * 60 * 1000) {
    return res.status(401).json({
      success: false,
      error: { code: 'TIMESTAMP_EXPIRED', message: '请求已过期' }
    });
  }

  const requestData = req.method === 'GET' ? req.query : req.body;
  const expected = crypto.createHmac('sha256', TOKEN_SECRET)
    .update(JSON.stringify(requestData || {}) + timestamp + token)
    .digest('hex');

  if (expected !== signature) {
    return res.status(401).json({
      success: false,
      error: { code: 'INVALID_SIGNATURE', message: '签名校验失败' }
    });
  }

  next();
}

// ==================== 路由定义 ====================

// 健康检查
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    await ensureTablesFromSql();
    
    // 获取缓存统计
    const cacheStats = await fileService.getCacheStats();
    
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok',
        storage: 'ok',
        memory: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`
      },
      cache: cacheStats
    };

    res.json(health);
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'error',
        storage: 'unknown',
        memory: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`
      }
    });
  }
});

// 企业微信Webhook
app.get('/api/wechat/webhook', async (req, res) => {
  console.log('🔍 处理【开启接收】企业微信URL验证请求');
  try {
    const result = await controller.verifyWebhookUrl(req);
    res.status(result.statusCode || 200).send(result.body || result);
  } catch (error) {
    console.error('Webhook验证失败:', error);
    res.status(500).json({ success: false, error: '验证失败' });
  }
});

app.post('/api/wechat/webhook', async (req, res) => {
  console.log('📨 处理【使用接收】企业微信消息推送请求');
  console.log('请求头:', req.headers);
  console.log('请求体类型:', typeof req.body);
  console.log('请求体内容:', req.body);
  try {
    await controller.handleWeChatMessage(req, res);
  } catch (error) {
    console.error('消息处理失败:', error);
    res.status(500).json({ success: false, error: '消息处理失败' });
  }
});

// 消息同步API（需要鉴权）
app.get('/api/sync/messages', verifySignature, async (req, res) => {
  try {
    await controller.syncMessages(req, res);
  } catch (error) {
    console.error('消息同步失败:', error);
    res.status(500).json({ success: false, error: '消息同步失败' });
  }
});

app.post('/api/sync/ack', verifySignature, async (req, res) => {
  try {
    await controller.updateSyncStatus(req, res);
  } catch (error) {
    console.error('同步状态更新失败:', error);
    res.status(500).json({ success: false, error: '同步状态更新失败' });
  }
});

// 设备管理API（需要鉴权）
app.post('/api/sync/register-device', verifySignature, async (req, res) => {
  try {
    await controller.registerDevice(req, res);
  } catch (error) {
    console.error('设备注册失败:', error);
    res.status(500).json({ success: false, error: '设备注册失败' });
  }
});

// 绑定管理API（需要鉴权）
app.get('/api/bind/status', verifySignature, async (req, res) => {
  try {
    await controller.getBindingByUserUuid(req, res);
  } catch (error) {
    console.error('绑定状态查询失败:', error);
    res.status(500).json({ success: false, error: '绑定状态查询失败' });
  }
});

app.post('/api/bind/one-click-link', verifySignature, async (req, res) => {
  try {
    await controller.generateOneClickBindingLink(req, res);
  } catch (error) {
    console.error('一键绑定链接生成失败:', error);
    res.status(500).json({ success: false, error: '一键绑定链接生成失败' });
  }
});

app.delete('/api/bind/unbind', verifySignature, async (req, res) => {
  try {
    await controller.unbindUser(req, res);
  } catch (error) {
    console.error('解除绑定失败:', error);
    res.status(500).json({ success: false, error: '解除绑定失败' });
  }
});

// 文件下载API
app.get('/api/media/download/:token', async (req, res) => {
  try {
    const { token } = req.params;
    await fileService.handleFileDownload(token, res);
  } catch (error) {
    console.error('文件下载API错误:', error);
    res.status(500).json({ success: false, error: '文件下载失败' });
  }
});

// 媒体文件下载API（用于微信转发的媒体文件）
app.get('/api/media/wechat/:mediaId', verifySignature, async (req, res) => {
  try {
    const { mediaId } = req.params;
    const { user_uuid } = req.query;

    console.log('📱 处理媒体文件下载请求:', { mediaId, user_uuid });

    // 调用微信API服务下载媒体文件
    await controller.downloadWeChatMedia(req, res);
  } catch (error) {
    console.error('微信媒体下载API错误:', error);
    res.status(500).json({
      success: false,
      error: { code: 'DOWNLOAD_ERROR', message: '文件下载失败' }
    });
  }
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: { code: 'NOT_FOUND', message: `API路径未找到: ${req.originalUrl}` }
  });
});

// 全局错误处理
app.use(async (error, req, res, next) => {
  console.error('全局错误处理:', error);
  
  await logError('Express 全局错误处理', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误'
    }
  });
});

// ==================== 定时任务 ====================

// 每小时清理过期文件
cron.schedule('0 * * * *', async () => {
  console.log('🧹 开始执行定时文件清理任务');
  try {
    await fileService.cleanupExpiredFiles();
  } catch (error) {
    console.error('定时文件清理失败:', error);
  }
});

// 每天凌晨2点清理数据库
cron.schedule('0 2 * * *', async () => {
  console.log('🗄️ 开始执行定时数据库清理任务');
  try {
    const { main: cleanupDatabase } = require('./scripts/cleanup-database');
    await cleanupDatabase();
  } catch (error) {
    console.error('定时数据库清理失败:', error);
  }
});

// ==================== 服务器启动 ====================

async function startServer() {
  try {
    console.log('🚀 正在初始化服务器...');
    
    // 初始化数据库
    await ensureTablesFromSql();
    console.log('✅ 数据库初始化完成');

    // 启动HTTP服务器
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`🌟 公职猫微信转发服务已启动`);
      console.log(`📡 服务地址: http://0.0.0.0:${PORT}`);
      console.log(`🏥 健康检查: http://0.0.0.0:${PORT}/health`);
      console.log(`🔗 Webhook地址: http://0.0.0.0:${PORT}/api/wechat/webhook`);
      console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log('==========================================');
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n📴 收到${signal}信号，正在优雅关闭服务器...`);
      server.close(() => {
        console.log('✅ HTTP服务器已关闭');
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = app; 