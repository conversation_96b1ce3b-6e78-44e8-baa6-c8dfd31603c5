#!/usr/bin/env node
/**
 * 轻量服务器环境变量检查工具
 * 用于验证轻量服务器的环境变量是否正确设置
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 加载环境变量
const wechatRoot = path.resolve(__dirname, '..');
require('dotenv').config({ path: path.resolve(wechatRoot, '.env') });

// 腾讯云配置
const config = {
  secretId: process.env.TENCENT_SECRET_ID,
  secretKey: process.env.TENCENT_SECRET_KEY,
  region: 'ap-guangzhou',
  functionName: 'gongzhimall-wechat-service'
};

// TC3签名算法
function generateTC3Signature(action, payload, timestamp) {
  const service = 'scf';
  const version = '2018-04-16';
  const algorithm = 'TC3-HMAC-SHA256';
  const date = new Date(timestamp * 1000).toISOString().substr(0, 10);
  
  const httpRequestMethod = 'POST';
  const canonicalUri = '/';
  const canonicalQueryString = '';
  const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:scf.tencentcloudapi.com\n`;
  const signedHeaders = 'content-type;host';
  const hashedRequestPayload = crypto.createHash('sha256').update(payload).digest('hex');
  const canonicalRequest = `${httpRequestMethod}\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}`;
  
  const credentialScope = `${date}/${service}/tc3_request`;
  const hashedCanonicalRequest = crypto.createHash('sha256').update(canonicalRequest).digest('hex');
  const stringToSign = `${algorithm}\n${timestamp}\n${credentialScope}\n${hashedCanonicalRequest}`;
  
  const secretDate = crypto.createHmac('sha256', `TC3${config.secretKey}`).update(date).digest();
  const secretService = crypto.createHmac('sha256', secretDate).update(service).digest();
  const secretSigning = crypto.createHmac('sha256', secretService).update('tc3_request').digest();
  const signature = crypto.createHmac('sha256', secretSigning).update(stringToSign).digest('hex');
  
  const authorization = `${algorithm} Credential=${config.secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  
  return authorization;
}

// 调用腾讯云API
async function callTencentAPI(action, params = {}) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payload = JSON.stringify(params);

  const authorization = generateTC3Signature(action, payload, timestamp);

  const curlCmd = `curl -X POST "https://scf.tencentcloudapi.com/" \
    -H "Authorization: ${authorization}" \
    -H "Content-Type: application/json; charset=utf-8" \
    -H "Host: scf.tencentcloudapi.com" \
    -H "X-TC-Action: ${action}" \
    -H "X-TC-Timestamp: ${timestamp}" \
    -H "X-TC-Version: 2018-04-16" \
    -H "X-TC-Region: ${config.region}" \
    -d '${payload.replace(/'/g, "'\"'\"'")}'`;

  try {
    const result = execSync(curlCmd, { encoding: 'utf8' });
    const json = JSON.parse(result);
    if (json.Response && json.Response.Error) {
      throw new Error(json.Response.Error.Message);
    }
    return json.Response || json;
  } catch (error) {
    throw error;
  }
}

// 获取期望的环境变量
function getExpectedEnvVars() {
  const requiredVars = [
    'MYSQL_HOST',
    'MYSQL_PORT',
    'MYSQL_DATABASE',
    'MYSQL_USER',
    'MYSQL_PASSWORD',
    'WECHAT_CORP_ID',
    'WECHAT_CORP_SECRET',
    'WECHAT_AGENT_ID',
    'WECHAT_TOKEN',
    'WECHAT_ENCODING_AES_KEY',
    'JPUSH_APP_KEY',
    'JPUSH_MASTER_SECRET'
  ];
  
  const optionalVars = [
    'FCM_SERVER_KEY',
    'FCM_PROJECT_ID',
    'APNS_KEY_ID',
    'APNS_TEAM_ID',
    'APNS_PRIVATE_KEY_PATH',
    'HMS_CLIENT_ID',
    'HMS_CLIENT_SECRET',
    'TOKEN_SECRET',
    'BINDING_SECRET',
    'LOG_LEVEL',
    'ENABLE_DB_LOGGING',
    'DEBUG',
    'NODE_ENV'
  ];
  
  const envVars = {};
  
  // 添加必需变量
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      envVars[varName] = process.env[varName];
    }
  });
  
  // 添加可选变量
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      envVars[varName] = process.env[varName];
    }
  });
  
  // 设置默认值
  envVars.NODE_ENV = envVars.NODE_ENV || 'production';
  envVars.LOG_LEVEL = envVars.LOG_LEVEL || 'INFO';
  envVars.ENABLE_DB_LOGGING = envVars.ENABLE_DB_LOGGING || 'true';
  envVars.DEBUG = envVars.DEBUG || 'false';
  envVars.MYSQL_PORT = envVars.MYSQL_PORT || '3306';
  envVars.TOKEN_SECRET = envVars.TOKEN_SECRET || 'gongzhimall-app-2025';
  envVars.BINDING_SECRET = envVars.BINDING_SECRET || 'gongzhimall_binding_secret_2025';
  
  return envVars;
}

// 主函数
async function main() {
  try {
    console.log('🔍 检查云函数环境变量...');
    console.log(`📋 函数名: ${config.functionName}`);
    console.log(`📍 区域: ${config.region}`);
    console.log('');
    
    // 获取云函数信息
    const result = await callTencentAPI('GetFunction', {
      FunctionName: config.functionName
    });
    
    if (result.Error) {
      console.error('❌ 获取函数信息失败:', result.Error.Message);
      return;
    }
    
    // 获取当前环境变量
    const currentEnv = {};
    if (result.Environment && result.Environment.Variables) {
      result.Environment.Variables.forEach(({ Key, Value }) => {
        currentEnv[Key] = Value;
      });
    }
    
    // 获取期望的环境变量
    const expectedEnv = getExpectedEnvVars();
    
    console.log('📊 环境变量对比结果:');
    console.log('');
    
    // 检查必需的环境变量
    const requiredVars = [
      'MYSQL_HOST',
      'MYSQL_PORT',
      'MYSQL_DATABASE',
      'MYSQL_USER',
      'MYSQL_PASSWORD',
      'WECHAT_CORP_ID',
      'WECHAT_CORP_SECRET',
      'WECHAT_AGENT_ID',
      'WECHAT_TOKEN',
      'WECHAT_ENCODING_AES_KEY',
      'JPUSH_APP_KEY',
      'JPUSH_MASTER_SECRET'
    ];
    
    let allGood = true;
    let successCount = 0;
    
    console.log('🔴 必需环境变量检查:');
    requiredVars.forEach(key => {
      const expected = expectedEnv[key];
      const actual = currentEnv[key];
      const isSecret = key.includes('PASSWORD') || key.includes('SECRET') || key.includes('KEY');
      
      if (!expected) {
        console.log(`  ❌ ${key}: 本地未配置`);
        allGood = false;
      } else if (!actual) {
        console.log(`  ❌ ${key}: 云函数中未设置`);
        allGood = false;
      } else if (actual !== expected) {
        console.log(`  ❌ ${key}: 不匹配 (期望: ${isSecret ? '***' : expected}, 实际: ${isSecret ? '***' : actual})`);
        allGood = false;
      } else {
        console.log(`  ✅ ${key}: 匹配`);
        successCount++;
      }
    });
    
    console.log('');
    console.log('🟡 可选环境变量检查:');
    const optionalVars = [
      'FCM_SERVER_KEY',
      'FCM_PROJECT_ID',
      'APNS_KEY_ID',
      'APNS_TEAM_ID',
      'APNS_PRIVATE_KEY_PATH',
      'HMS_CLIENT_ID',
      'HMS_CLIENT_SECRET',
      'TOKEN_SECRET',
      'BINDING_SECRET',
      'LOG_LEVEL',
      'ENABLE_DB_LOGGING',
      'DEBUG',
      'NODE_ENV'
    ];
    
    optionalVars.forEach(key => {
      const expected = expectedEnv[key];
      const actual = currentEnv[key];
      const isSecret = key.includes('PASSWORD') || key.includes('SECRET') || key.includes('KEY');
      
      if (!expected) {
        console.log(`  ⚪ ${key}: 本地未配置`);
      } else if (!actual) {
        console.log(`  ⚠️  ${key}: 云函数中未设置`);
      } else if (actual !== expected) {
        console.log(`  ⚠️  ${key}: 不匹配 (期望: ${isSecret ? '***' : expected}, 实际: ${isSecret ? '***' : actual})`);
      } else {
        console.log(`  ✅ ${key}: 匹配`);
      }
    });
    
    console.log('');
    console.log('🟢 云函数中存在但本地未配置的变量:');
    const extraVars = Object.keys(currentEnv).filter(key => !expectedEnv[key]);
    if (extraVars.length === 0) {
      console.log('  ✅ 没有多余的环境变量');
    } else {
      extraVars.forEach(key => {
        console.log(`  ⚠️  ${key}: ${currentEnv[key]}`);
      });
    }
    
    console.log('');
    console.log('📈 总结:');
    console.log(`必需环境变量: ${successCount}/${requiredVars.length} 正确`);
    console.log(`成功率: ${Math.round(successCount/requiredVars.length*100)}%`);
    
    if (allGood) {
      console.log('✅ 所有必需的环境变量都已正确设置');
    } else {
      console.log('❌ 存在环境变量配置问题，请检查并重新部署');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main }; 