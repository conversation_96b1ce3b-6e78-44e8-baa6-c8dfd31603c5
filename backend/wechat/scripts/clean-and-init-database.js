#!/usr/bin/env node

/**
 * 数据库清理和初始化脚本
 * 用于在干净环境中重新创建微信转发服务数据库
 * 基于现有的database.js模块进行重构
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// 使用与database.js相同的配置
const DB_CONFIG = {
    host: process.env.MYSQL_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || '3306'),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    charset: 'utf8mb4',
    timezone: '+08:00',
    multipleStatements: true
};

const DATABASE_NAME = process.env.MYSQL_DATABASE || 'gongzhimall_wechat';

// 日志函数
const log = (message) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
};

const error = (message) => {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
};

const success = (message) => {
    console.log(`[${new Date().toISOString()}] ✅ ${message}`);
};

/**
 * 检查数据库连接
 */
async function checkConnection() {
    log('检查数据库连接...');
    
    try {
        const connection = await mysql.createConnection(DB_CONFIG);
        await connection.execute('SELECT 1');
        await connection.end();
        success('数据库连接正常');
        return true;
    } catch (err) {
        error(`数据库连接失败: ${err.message}`);
        return false;
    }
}

/**
 * 删除现有数据库
 */
async function dropDatabase() {
    log(`删除现有数据库: ${DATABASE_NAME}`);
    
    try {
        const connection = await mysql.createConnection(DB_CONFIG);
        
        // 检查数据库是否存在
        const [databases] = await connection.execute(
            `SHOW DATABASES LIKE '${DATABASE_NAME}'`
        );

        if (databases.length > 0) {
            await connection.execute(`DROP DATABASE IF EXISTS \`${DATABASE_NAME}\``);
            success(`数据库 ${DATABASE_NAME} 已删除`);
        } else {
            log(`数据库 ${DATABASE_NAME} 不存在，跳过删除`);
        }
        
        await connection.end();
    } catch (err) {
        error(`删除数据库失败: ${err.message}`);
        throw err;
    }
}

/**
 * 创建数据库用户（跳过，使用现有用户）
 */
async function createDatabaseUser() {
    log('跳过数据库用户创建（使用现有用户）...');
    success('使用现有数据库用户');
}

/**
 * 执行SQL文件（借鉴database.js的ensureTablesFromSql方法）
 */
async function executeSqlFile() {
    log('执行数据库初始化脚本...');

    let connection;
    try {
        const sqlFilePath = path.join(__dirname, '../data/database.sql');

        if (!await fs.access(sqlFilePath).then(() => true).catch(() => false)) {
            error('database.sql 文件不存在');
            throw new Error('database.sql 文件不存在');
        }

        const sqlContent = await fs.readFile(sqlFilePath, 'utf8');
        connection = await mysql.createConnection(DB_CONFIG);

        // 更智能的SQL语句分割
        const statements = [];
        const lines = sqlContent.split('\n');
        let currentStatement = '';

        for (const line of lines) {
            const trimmedLine = line.trim();

            // 跳过注释行和空行
            if (trimmedLine === '' || trimmedLine.startsWith('--')) {
                continue;
            }

            currentStatement += line + '\n';

            // 如果行以分号结尾，表示语句结束
            if (trimmedLine.endsWith(';')) {
                const statement = currentStatement.trim();
                if (statement && statement !== ';') {
                    statements.push(statement);
                }
                currentStatement = '';
            }
        }

        // 处理最后一个语句（如果没有以分号结尾）
        if (currentStatement.trim()) {
            statements.push(currentStatement.trim());
        }

        log(`在 database.sql 中找到 ${statements.length} 条SQL语句，开始逐条执行...`);

        for (let i = 0; i < statements.length; i++) {
            const stmt = statements[i];
            if (stmt) {
                try {
                    log(`执行语句 ${i + 1}/${statements.length}: ${stmt.substring(0, 50)}...`);
                    await connection.query(stmt);
                    success(`语句 ${i + 1} 执行成功`);
                } catch (err) {
                    // 忽略一些可能的警告
                    if (!err.message.includes('already exists') &&
                        !err.message.includes('Duplicate entry') &&
                        !err.message.includes('Duplicate key name')) {
                        console.warn(`SQL警告: ${err.message}`);
                        console.warn(`语句: ${stmt.substring(0, 100)}...`);
                    }
                }
            }
        }

        success('数据库初始化脚本执行完成');
    } catch (err) {
        error(`执行SQL文件失败: ${err.message}`);
        throw err;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

/**
 * 验证数据库结构
 */
async function verifyDatabase() {
    log('验证数据库结构...');
    
    try {
        const connection = await mysql.createConnection({
            ...DB_CONFIG,
            database: DATABASE_NAME
        });
        
        // 检查主要表是否存在
        const expectedTables = [
            'app_users',
            'wechat_bindings',
            'user_device_bindings',
            'wechat_message_logs',
            'system_logs',
            'wechat_sync_configs',
            'system_configs',
            'access_token_cache',
            'kf_link_cache',
            'token_api_calls',
            'kf_sync_cursors',
            'short_term_tokens'
        ];
        
        const [tables] = await connection.execute('SHOW TABLES');
        const existingTables = tables.map(row => Object.values(row)[0]);
        
        let allTablesExist = true;
        for (const table of expectedTables) {
            if (existingTables.includes(table)) {
                success(`表 ${table} 存在`);
            } else {
                error(`表 ${table} 不存在`);
                allTablesExist = false;
            }
        }
        
        // 检查系统配置是否插入
        const [configs] = await connection.execute('SELECT COUNT(*) as count FROM system_configs');
        const configCount = configs[0].count;
        
        if (configCount > 0) {
            success(`系统配置已插入 (${configCount} 条记录)`);
        } else {
            error('系统配置未插入');
            allTablesExist = false;
        }
        
        await connection.end();
        
        if (allTablesExist) {
            success('数据库结构验证通过');
        } else {
            throw new Error('数据库结构验证失败');
        }
    } catch (err) {
        error(`验证数据库失败: ${err.message}`);
        throw err;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始清理和初始化微信转发服务数据库...\n');
    
    try {
        // 1. 检查数据库连接
        const connected = await checkConnection();
        if (!connected) {
            throw new Error('无法连接到数据库');
        }
        
        // 2. 删除现有数据库
        await dropDatabase();
        
        // 3. 创建数据库用户
        await createDatabaseUser();
        
        // 4. 执行SQL初始化脚本
        await executeSqlFile();
        
        // 5. 验证数据库结构
        await verifyDatabase();
        
        console.log('\n🎉 数据库清理和初始化完成！');
        console.log(`数据库名称: ${DATABASE_NAME}`);
        console.log(`数据库地址: ${DB_CONFIG.host}:${DB_CONFIG.port}`);
        console.log('现在可以开始部署微信转发服务了。');
        
    } catch (err) {
        console.error('\n❌ 数据库初始化失败:');
        console.error(err.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    checkConnection,
    dropDatabase,
    createDatabaseUser,
    executeSqlFile,
    verifyDatabase
};
