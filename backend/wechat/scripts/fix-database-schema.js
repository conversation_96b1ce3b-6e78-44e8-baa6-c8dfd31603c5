/**
 * 数据库表结构修复脚本
 * 解决缺少 media_id 和 media_id_expires_at 字段的问题
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.MYSQL_HOST,
  port: process.env.MYSQL_PORT,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  charset: 'utf8mb4'
};

/**
 * 执行SQL语句
 */
async function executeSQL(connection, sql, description) {
  try {
    console.log(`🔧 执行: ${description}`);
    const [result] = await connection.execute(sql);
    console.log(`✅ 成功: ${description}`);
    return result;
  } catch (error) {
    if (error.code === 'ER_DUP_FIELDNAME' || error.message.includes('Duplicate column name')) {
      console.log(`⚠️  跳过: ${description} (字段已存在)`);
      return null;
    } else if (error.code === 'ER_DUP_KEYNAME' || error.message.includes('Duplicate key name')) {
      console.log(`⚠️  跳过: ${description} (索引已存在)`);
      return null;
    } else {
      console.error(`❌ 失败: ${description}`);
      console.error(`错误: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 修复数据库表结构
 */
async function fixDatabaseSchema() {
  let connection;
  
  try {
    console.log('🚀 开始修复数据库表结构...');
    console.log('数据库配置:', {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      database: dbConfig.database
    });
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 添加缺失的字段
    const alterStatements = [
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN media_id VARCHAR(128) NULL COMMENT '企业微信媒体文件ID'`,
        description: '添加 media_id 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN media_id_expires_at TIMESTAMP NULL COMMENT '媒体文件ID过期时间（3天）'`,
        description: '添加 media_id_expires_at 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN file_size BIGINT NULL COMMENT '文件大小（字节）'`,
        description: '添加 file_size 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN content_type VARCHAR(200) NULL COMMENT '文件MIME类型'`,
        description: '添加 content_type 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN download_token TEXT NULL COMMENT '下载令牌（JWT）'`,
        description: '添加 download_token 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN download_expires_at TIMESTAMP NULL COMMENT '下载链接过期时间（24小时）'`,
        description: '添加 download_expires_at 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN file_expires_at TIMESTAMP NULL COMMENT '文件过期时间（3天后删除）'`,
        description: '添加 file_expires_at 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN downloaded BOOLEAN DEFAULT FALSE COMMENT '是否已被用户下载（阅后即焚标记）'`,
        description: '添加 downloaded 字段'
      },
      {
        sql: `ALTER TABLE wechat_message_logs ADD COLUMN downloaded_at TIMESTAMP NULL COMMENT '下载时间'`,
        description: '添加 downloaded_at 字段'
      }
    ];
    
    // 执行字段添加
    for (const statement of alterStatements) {
      await executeSQL(connection, statement.sql, statement.description);
    }
    
    // 添加索引
    const indexStatements = [
      {
        sql: `CREATE INDEX idx_media_id ON wechat_message_logs(media_id)`,
        description: '创建 media_id 索引'
      },
      {
        sql: `CREATE INDEX idx_media_id_expires ON wechat_message_logs(media_id_expires_at)`,
        description: '创建 media_id_expires_at 索引'
      },
      {
        sql: `CREATE INDEX idx_file_expires ON wechat_message_logs(file_expires_at)`,
        description: '创建 file_expires_at 索引'
      },
      {
        sql: `CREATE INDEX idx_download_expires ON wechat_message_logs(download_expires_at)`,
        description: '创建 download_expires_at 索引'
      },
      {
        sql: `CREATE INDEX idx_downloaded ON wechat_message_logs(downloaded)`,
        description: '创建 downloaded 索引'
      }
    ];
    
    // 执行索引创建
    for (const statement of indexStatements) {
      await executeSQL(connection, statement.sql, statement.description);
    }
    
    // 显示表结构
    console.log('\n📋 当前表结构:');
    const [columns] = await connection.execute('DESCRIBE wechat_message_logs');
    console.table(columns);
    
    console.log('\n🎉 数据库表结构修复完成!');
    return true;
    
  } catch (error) {
    console.error('❌ 数据库表结构修复失败:', error);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 公职猫微信转发 - 数据库表结构修复');
  console.log('=' * 50);
  
  const success = await fixDatabaseSchema();
  
  console.log('\n' + '=' * 50);
  if (success) {
    console.log('🎉 数据库表结构修复成功!');
  } else {
    console.log('💥 数据库表结构修复失败');
  }
  
  process.exit(success ? 0 : 1);
}

// 运行修复
main().catch(error => {
  console.error('修复脚本执行失败:', error);
  process.exit(1);
});
