/**
 * 测试环境配置验证脚本
 * 验证所有必需的环境变量、数据库连接、Webhook可访问性、JPush配置等
 */

const https = require('https');
const http = require('http');
const mysql = require('mysql2/promise');

// 模拟基础环境变量（如果未设置）
if (!process.env.WECHAT_TOKEN) {
  process.env.WECHAT_TOKEN = 'test_token';
  process.env.WECHAT_ENCODING_AES_KEY = 'test_key_1234567890123456789012345678901234567890123';
  process.env.WECHAT_CORP_ID = 'test_corp_id';
}

console.log('🧪 测试环境配置验证...\n');

/**
 * 验证环境变量配置
 */
function validateEnvironmentVariables() {
  console.log('📋 验证环境变量配置...');
  
  const requiredVars = {
    // 企业微信配置
    'WECHAT_TOKEN': '企业微信Token',
    'WECHAT_ENCODING_AES_KEY': '企业微信AES密钥',
    'WECHAT_CORP_ID': '企业微信CorpID',
    
    // 数据库配置
    'MYSQL_HOST': 'MySQL主机地址',
    'MYSQL_USER': 'MySQL用户名',
    'MYSQL_PASSWORD': 'MySQL密码',
    'MYSQL_DATABASE': 'MySQL数据库名'
  };
  
  const optionalVars = {
    // JPush配置
    'JPUSH_APP_KEY': 'JPush应用Key',
    'JPUSH_MASTER_SECRET': 'JPush主密钥',
    
    // 服务配置
    'PORT': '服务端口',
    'NODE_ENV': '运行环境',
    'WECHAT_BINDING_TOKEN_SECRET': '绑定Token密钥'
  };
  
  let missingRequired = [];
  let missingOptional = [];
  
  // 检查必需变量
  for (const [varName, description] of Object.entries(requiredVars)) {
    if (process.env[varName]) {
      const value = process.env[varName];
      const maskedValue = varName.includes('PASSWORD') || varName.includes('SECRET') || varName.includes('KEY')
        ? value.substring(0, 8) + '...'
        : value;
      console.log(`✅ ${varName}: ${maskedValue} (${description})`);
    } else {
      missingRequired.push({ varName, description });
    }
  }
  
  // 检查可选变量
  for (const [varName, description] of Object.entries(optionalVars)) {
    if (process.env[varName]) {
      const value = process.env[varName];
      const maskedValue = varName.includes('PASSWORD') || varName.includes('SECRET') || varName.includes('KEY')
        ? value.substring(0, 8) + '...'
        : value;
      console.log(`✅ ${varName}: ${maskedValue} (${description})`);
    } else {
      missingOptional.push({ varName, description });
    }
  }
  
  // 报告结果
  if (missingRequired.length > 0) {
    console.log('\n❌ 缺少必需的环境变量:');
    missingRequired.forEach(({ varName, description }) => {
      console.log(`   ${varName}: ${description}`);
    });
    return { success: false, missingRequired, missingOptional };
  }
  
  if (missingOptional.length > 0) {
    console.log('\n⚠️  缺少可选的环境变量:');
    missingOptional.forEach(({ varName, description }) => {
      console.log(`   ${varName}: ${description}`);
    });
  }
  
  console.log('\n✅ 环境变量验证通过');
  return { success: true, missingRequired: [], missingOptional };
}

/**
 * 测试数据库连接
 */
async function testDatabaseConnection() {
  console.log('\n💾 测试数据库连接...');
  
  if (!process.env.MYSQL_HOST) {
    console.log('⚠️  数据库配置未设置，跳过连接测试');
    return { success: true, skipped: true };
  }
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
      connectTimeout: 10000
    });
    
    // 测试基本查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库连接成功');
    
    // 检查表是否存在
    const tables = [
      'app_users',
      'wechat_bindings', 
      'user_device_bindings',
      'wechat_message_logs',
      'system_configs'
    ];
    
    for (const table of tables) {
      try {
        const [tableRows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ 表 ${table}: ${tableRows[0].count} 条记录`);
      } catch (error) {
        console.log(`❌ 表 ${table}: 不存在或无法访问`);
        await connection.end();
        return { success: false, error: `表 ${table} 不存在` };
      }
    }
    
    await connection.end();
    return { success: true };
    
  } catch (error) {
    console.log(`❌ 数据库连接失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 测试Webhook URL可访问性
 */
async function testWebhookAccessibility() {
  console.log('\n🔗 测试Webhook URL可访问性...');
  
  const webhookUrl = process.env.WEBHOOK_URL || 'https://wechat.api.gongzhimall.com/api/wechat/webhook';
  
  return new Promise((resolve) => {
    const url = new URL(webhookUrl);
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request({
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + '?echostr=test&timestamp=123456&nonce=test&msg_signature=test',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      console.log(`✅ Webhook URL可访问 (状态码: ${res.statusCode})`);
      resolve({ success: true, statusCode: res.statusCode });
    });
    
    req.on('error', (error) => {
      console.log(`❌ Webhook URL不可访问: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
    
    req.on('timeout', () => {
      console.log('❌ Webhook URL访问超时');
      req.destroy();
      resolve({ success: false, error: '访问超时' });
    });
    
    req.end();
  });
}

/**
 * 验证SSL证书
 */
async function validateSSLCertificate() {
  console.log('\n🔒 验证SSL证书...');
  
  const webhookUrl = process.env.WEBHOOK_URL || 'https://wechat.api.gongzhimall.com';
  
  if (!webhookUrl.startsWith('https://')) {
    console.log('⚠️  非HTTPS URL，跳过SSL验证');
    return { success: true, skipped: true };
  }
  
  return new Promise((resolve) => {
    const url = new URL(webhookUrl);
    
    const req = https.request({
      hostname: url.hostname,
      port: 443,
      path: '/',
      method: 'GET',
      timeout: 10000
    }, (res) => {
      const cert = res.socket.getPeerCertificate();
      if (cert && cert.valid_to) {
        const expiryDate = new Date(cert.valid_to);
        const daysUntilExpiry = Math.ceil((expiryDate - new Date()) / (1000 * 60 * 60 * 24));
        
        if (daysUntilExpiry > 30) {
          console.log(`✅ SSL证书有效 (${daysUntilExpiry} 天后过期)`);
          resolve({ success: true, daysUntilExpiry });
        } else {
          console.log(`⚠️  SSL证书即将过期 (${daysUntilExpiry} 天后过期)`);
          resolve({ success: true, warning: true, daysUntilExpiry });
        }
      } else {
        console.log('❌ 无法获取SSL证书信息');
        resolve({ success: false, error: '无法获取证书信息' });
      }
    });
    
    req.on('error', (error) => {
      console.log(`❌ SSL验证失败: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
    
    req.on('timeout', () => {
      console.log('❌ SSL验证超时');
      req.destroy();
      resolve({ success: false, error: '验证超时' });
    });
    
    req.end();
  });
}

/**
 * 验证JPush配置
 */
function validateJPushConfiguration() {
  console.log('\n📱 验证JPush推送配置...');
  
  const jpushAppKey = process.env.JPUSH_APP_KEY;
  const jpushMasterSecret = process.env.JPUSH_MASTER_SECRET;
  
  if (!jpushAppKey || !jpushMasterSecret) {
    console.log('⚠️  JPush配置未设置，推送功能将不可用');
    return { success: true, warning: true, message: 'JPush配置缺失' };
  }
  
  // 验证配置格式
  if (jpushAppKey.length < 20) {
    console.log('❌ JPush App Key格式不正确');
    return { success: false, error: 'App Key格式错误' };
  }
  
  if (jpushMasterSecret.length < 20) {
    console.log('❌ JPush Master Secret格式不正确');
    return { success: false, error: 'Master Secret格式错误' };
  }
  
  console.log(`✅ JPush配置格式正确`);
  console.log(`   App Key: ${jpushAppKey.substring(0, 8)}...`);
  console.log(`   Master Secret: ${jpushMasterSecret.substring(0, 8)}...`);
  
  return { success: true };
}

/**
 * 测试服务启动
 */
async function testServiceStartup() {
  console.log('\n🚀 测试服务启动...');
  
  try {
    // 尝试导入主要服务模块
    const mainService = require('../service/service');
    console.log('✅ 主服务模块导入成功');
    
    // 验证关键函数存在
    const requiredFunctions = [
      'verifyWebhookUrl',
      'processWeChatMessage',
      'getIncrementalMessages',
      'downloadWeChatMediaFile'
    ];
    
    for (const funcName of requiredFunctions) {
      if (typeof mainService[funcName] === 'function') {
        console.log(`✅ 函数 ${funcName}: 可用`);
      } else {
        console.log(`❌ 函数 ${funcName}: 不可用`);
        return { success: false, error: `函数 ${funcName} 不存在` };
      }
    }
    
    return { success: true };
    
  } catch (error) {
    console.log(`❌ 服务启动测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 主验证函数
 */
async function runEnvironmentValidation() {
  console.log('🚀 测试环境配置验证\n');
  console.log('=' .repeat(60));
  
  const results = {
    envVars: null,
    database: null,
    webhook: null,
    ssl: null,
    jpush: null,
    service: null
  };
  
  try {
    // 1. 验证环境变量
    results.envVars = validateEnvironmentVariables();
    
    // 2. 测试数据库连接
    results.database = await testDatabaseConnection();
    
    // 3. 测试Webhook可访问性
    results.webhook = await testWebhookAccessibility();
    
    // 4. 验证SSL证书
    results.ssl = await validateSSLCertificate();
    
    // 5. 验证JPush配置
    results.jpush = validateJPushConfiguration();
    
    // 6. 测试服务启动
    results.service = await testServiceStartup();
    
    // 总结
    console.log('\n' + '=' .repeat(60));
    console.log('📋 验证结果总结:');
    console.log(`   环境变量: ${results.envVars.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   数据库连接: ${results.database.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   Webhook可访问性: ${results.webhook.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   SSL证书: ${results.ssl.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   JPush配置: ${results.jpush.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   服务启动: ${results.service.success ? '✅ 通过' : '❌ 失败'}`);
    
    const criticalTests = [results.envVars, results.database, results.service];
    const allCriticalPassed = criticalTests.every(r => r && r.success);
    
    console.log(`\n🎯 总体结果: ${allCriticalPassed ? '✅ 环境准备就绪' : '❌ 环境配置有问题'}`);
    
    if (allCriticalPassed) {
      console.log('\n🎉 测试环境配置验证通过！');
      console.log('📱 可以开始Android真机测试了。');
    } else {
      console.log('\n⚠️ 发现配置问题，请修复后再进行测试。');
    }
    
    return results;
    
  } catch (error) {
    console.error('💥 验证执行失败:', error);
    return null;
  }
}

// 运行验证
if (require.main === module) {
  runEnvironmentValidation().catch(error => {
    console.error('💥 验证执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runEnvironmentValidation };
