#!/usr/bin/env node

/**
 * 新架构功能测试脚本
 * 验证文件服务、推送服务、数据库等核心功能
 */

require('dotenv').config();

async function testNewArchitecture() {
  console.log('🧪 开始测试新的微信转发架构...\n');
  
  const tests = [
    testFileService,
    testPushService,
    testDatabaseOperations,
    testAPIEndpoints
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      await test();
      passedTests++;
    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}\n`);
    }
  }
  
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！新架构准备就绪！');
    process.exit(0);
  } else {
    console.log('⚠️  部分测试失败，请检查配置');
    process.exit(1);
  }
}

/**
 * 测试文件服务
 */
async function testFileService() {
  console.log('📁 测试文件服务...');
  
  try {
    const fileService = require('../service/fileService');
    
    // 测试缓存目录初始化
    await fileService.initCacheDirectory();
    console.log('✅ 缓存目录初始化成功');
    
    // 测试获取缓存统计
    const stats = await fileService.getCacheStats();
    console.log(`✅ 缓存统计: ${stats.fileCount} 个文件, ${stats.totalSizeMB} MB`);
    
    // 测试加密/解密功能
    const testData = Buffer.from('测试数据');
    const encrypted = fileService.encryptData(testData);
    const decrypted = fileService.decryptData(encrypted);
    
    if (testData.equals(decrypted)) {
      console.log('✅ 文件加密/解密功能正常');
    } else {
      throw new Error('加密/解密测试失败');
    }
    
    // 测试JWT令牌生成
    const token = fileService.generateDownloadToken({
      messageId: 'test_msg_123',
      fileName: 'test.jpg',
      filePath: 'test_path.enc',
      contentType: 'image/jpeg',
      fileSize: 1024
    });
    
    const tokenData = fileService.verifyDownloadToken(token);
    if (tokenData && tokenData.messageId === 'test_msg_123') {
      console.log('✅ JWT令牌生成/验证功能正常');
    } else {
      throw new Error('JWT令牌测试失败');
    }
    
    console.log('✅ 文件服务测试通过\n');
  } catch (error) {
    throw new Error(`文件服务测试失败: ${error.message}`);
  }
}

/**
 * 测试推送服务
 */
async function testPushService() {
  console.log('📱 测试推送服务...');
  
  try {
    const pushService = require('../service/pushService');
    
    // 检查推送服务配置
    if (!process.env.JPUSH_APP_KEY || !process.env.JPUSH_MASTER_SECRET) {
      console.log('⚠️  极光推送配置未设置，跳过推送测试');
      return;
    }
    
    // 测试推送服务初始化
    console.log('✅ 推送服务配置检查通过');
    
    // 测试文件下载通知格式
    const mockDevices = [{
      device_id: 'test_device_123',
      platform: 'android',
      push_token: 'test_token_123',
      push_provider: 'jpush'
    }];
    
    const mockFileInfo = {
      messageId: 'test_msg_123',
      fileName: 'test_image.jpg',
      fileSize: 2048,
      contentType: 'image/jpeg',
      downloadUrl: '/api/media/download/test_token',
      downloadExpiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
      messageType: 'image'
    };
    
    // 注意：这里不实际发送推送，只是验证函数存在
    if (typeof pushService.sendFileDownloadNotification === 'function') {
      console.log('✅ 文件下载通知函数存在');
    } else {
      throw new Error('文件下载通知函数不存在');
    }
    
    console.log('✅ 推送服务测试通过\n');
  } catch (error) {
    throw new Error(`推送服务测试失败: ${error.message}`);
  }
}

/**
 * 测试数据库操作
 */
async function testDatabaseOperations() {
  console.log('🗄️  测试数据库操作...');
  
  try {
    const db = require('../data/database');
    
    // 测试数据库连接
    const testQuery = await db.query('SELECT 1 as test');
    if (testQuery && testQuery[0] && testQuery[0].test === 1) {
      console.log('✅ 数据库连接正常');
    } else {
      throw new Error('数据库连接测试失败');
    }
    
    // 检查新的数据库函数是否存在
    if (typeof db.saveMessageMetadata === 'function') {
      console.log('✅ saveMessageMetadata函数存在');
    } else {
      throw new Error('saveMessageMetadata函数不存在');
    }
    
    if (typeof db.markFileAsDownloaded === 'function') {
      console.log('✅ markFileAsDownloaded函数存在');
    } else {
      throw new Error('markFileAsDownloaded函数不存在');
    }
    
    if (typeof db.getIncrementalMessages === 'function') {
      console.log('✅ getIncrementalMessages函数存在');
    } else {
      throw new Error('getIncrementalMessages函数不存在');
    }
    
    console.log('✅ 数据库操作测试通过\n');
  } catch (error) {
    throw new Error(`数据库操作测试失败: ${error.message}`);
  }
}

/**
 * 测试API端点
 */
async function testAPIEndpoints() {
  console.log('🌐 测试API端点...');
  
  try {
    const express = require('express');
    const app = require('../index');
    
    // 检查Express应用是否正确创建
    if (app && typeof app.listen === 'function') {
      console.log('✅ Express应用创建成功');
    } else {
      throw new Error('Express应用创建失败');
    }
    
    // 检查环境变量配置
    const requiredEnvVars = [
      'PORT',
      'FILE_STORAGE_PATH',
      'FILE_ENCRYPTION_KEY',
      'TOKEN_SECRET',
      'DOWNLOAD_TOKEN_EXPIRES_HOURS'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      console.log(`⚠️  缺少环境变量: ${missingEnvVars.join(', ')}`);
      console.log('⚠️  请检查.env配置文件');
    } else {
      console.log('✅ 环境变量配置完整');
    }
    
    console.log('✅ API端点测试通过\n');
  } catch (error) {
    throw new Error(`API端点测试失败: ${error.message}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testNewArchitecture();
}

module.exports = testNewArchitecture; 