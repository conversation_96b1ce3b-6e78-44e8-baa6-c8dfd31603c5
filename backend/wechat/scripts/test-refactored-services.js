/**
 * 测试重构后的服务层功能完整性
 * 验证所有API接口是否正常工作
 */

const path = require('path');

// 模拟环境变量（测试用）
process.env.WECHAT_TOKEN = 'test_token';
process.env.WECHAT_ENCODING_AES_KEY = 'test_key_1234567890123456789012345678901234567890123';
process.env.WECHAT_CORP_ID = 'test_corp_id';
process.env.WECHAT_BINDING_TOKEN_SECRET = 'test_secret';

// 模拟数据库配置（避免数据库连接错误）
process.env.MYSQL_HOST = 'localhost';
process.env.MYSQL_USER = 'test';
process.env.MYSQL_PASSWORD = 'test';
process.env.MYSQL_DATABASE = 'test';

console.log('🧪 开始测试重构后的服务层功能完整性...\n');

/**
 * 测试服务文件导入
 */
async function testServiceImports() {
  console.log('📦 测试服务文件导入...');
  
  try {
    // 测试主service文件
    const mainService = require('../service/service');
    console.log('✅ 主service文件导入成功');
    console.log('   导出的函数数量:', Object.keys(mainService).length);
    
    // 测试各个子服务
    const WebhookService = require('../service/WebhookService');
    console.log('✅ WebhookService导入成功，导出函数:', Object.keys(WebhookService).length);
    
    const MessageProcessService = require('../service/MessageProcessService');
    console.log('✅ MessageProcessService导入成功，导出函数:', Object.keys(MessageProcessService).length);
    
    const UserBindingService = require('../service/UserBindingService');
    console.log('✅ UserBindingService导入成功，导出函数:', Object.keys(UserBindingService).length);
    
    const MessageSyncService = require('../service/MessageSyncService');
    console.log('✅ MessageSyncService导入成功，导出函数:', Object.keys(MessageSyncService).length);
    
    const MediaDownloadService = require('../service/MediaDownloadService');
    console.log('✅ MediaDownloadService导入成功，导出函数:', Object.keys(MediaDownloadService).length);
    
    return { success: true, mainService };
  } catch (error) {
    console.error('❌ 服务文件导入失败:', error.message);
    return { success: false, error };
  }
}

/**
 * 测试API接口完整性
 */
function testAPICompleteness(mainService) {
  console.log('\n🔍 测试API接口完整性...');
  
  // controller.js中使用的所有函数
  const requiredFunctions = [
    'verifyWebhookUrl',
    'verifyMessageSignature', 
    'processWeChatMessage',
    'getBindingByUserUuid',
    'generateWeChatBindingLink',
    'unbindUser',
    'getIncrementalMessages',
    'updateDeviceSyncStatus',
    'registerDevice',
    'getShortTermAccessToken',
    'downloadWeChatMediaFile'
  ];
  
  const missingFunctions = [];
  const availableFunctions = [];
  
  for (const funcName of requiredFunctions) {
    if (typeof mainService[funcName] === 'function') {
      availableFunctions.push(funcName);
      console.log(`✅ ${funcName} - 可用`);
    } else {
      missingFunctions.push(funcName);
      console.log(`❌ ${funcName} - 缺失`);
    }
  }
  
  console.log(`\n📊 API完整性统计:`);
  console.log(`   可用函数: ${availableFunctions.length}/${requiredFunctions.length}`);
  console.log(`   缺失函数: ${missingFunctions.length}`);
  
  if (missingFunctions.length > 0) {
    console.log(`   缺失的函数: ${missingFunctions.join(', ')}`);
  }
  
  return {
    success: missingFunctions.length === 0,
    available: availableFunctions,
    missing: missingFunctions
  };
}

/**
 * 测试服务层架构
 */
function testServiceArchitecture(mainService) {
  console.log('\n🏗️ 测试服务层架构...');
  
  const allFunctions = Object.keys(mainService);
  const categories = {
    webhook: allFunctions.filter(f => f.includes('verify') || f.includes('decrypt') || f.includes('parse')),
    message: allFunctions.filter(f => f.includes('process') && f.includes('Message')),
    binding: allFunctions.filter(f => f.includes('Binding') || f.includes('bind')),
    sync: allFunctions.filter(f => f.includes('sync') || f.includes('Incremental') || f.includes('Device')),
    media: allFunctions.filter(f => f.includes('download') || f.includes('Media') || f.includes('File'))
  };
  
  console.log('📋 服务分类统计:');
  console.log(`   Webhook处理: ${categories.webhook.length} 个函数`);
  console.log(`   消息处理: ${categories.message.length} 个函数`);
  console.log(`   用户绑定: ${categories.binding.length} 个函数`);
  console.log(`   消息同步: ${categories.sync.length} 个函数`);
  console.log(`   媒体下载: ${categories.media.length} 个函数`);
  
  const totalCategorized = Object.values(categories).reduce((sum, arr) => sum + arr.length, 0);
  const uncategorized = allFunctions.length - totalCategorized;
  
  console.log(`   未分类: ${uncategorized} 个函数`);
  console.log(`   总计: ${allFunctions.length} 个函数`);
  
  return {
    success: true,
    categories,
    totalFunctions: allFunctions.length,
    uncategorized
  };
}

/**
 * 测试配置验证
 */
function testConfigValidation(mainService) {
  console.log('\n⚙️ 测试配置验证...');
  
  try {
    if (typeof mainService.validateConfig === 'function') {
      const configResult = mainService.getConfigValidation();
      console.log('✅ 配置验证功能可用');
      console.log(`   配置状态: ${configResult.valid ? '有效' : '无效'}`);
      
      if (!configResult.valid) {
        console.log(`   缺失配置: ${configResult.missing?.join(', ') || '未知'}`);
      }
      
      return { success: true, configValid: configResult.valid };
    } else {
      console.log('❌ 配置验证功能不可用');
      return { success: false };
    }
  } catch (error) {
    console.log('❌ 配置验证测试失败:', error.message);
    return { success: false, error };
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 重构后服务层功能完整性测试\n');
  console.log('=' .repeat(50));
  
  const results = {
    imports: null,
    api: null,
    architecture: null,
    config: null
  };
  
  // 1. 测试服务导入
  results.imports = await testServiceImports();
  if (!results.imports.success) {
    console.log('\n❌ 服务导入测试失败，停止后续测试');
    return results;
  }
  
  // 2. 测试API完整性
  results.api = testAPICompleteness(results.imports.mainService);
  
  // 3. 测试服务架构
  results.architecture = testServiceArchitecture(results.imports.mainService);
  
  // 4. 测试配置验证
  results.config = testConfigValidation(results.imports.mainService);
  
  // 总结
  console.log('\n' + '=' .repeat(50));
  console.log('📋 测试结果总结:');
  console.log(`   服务导入: ${results.imports.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   API完整性: ${results.api.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   服务架构: ${results.architecture.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   配置验证: ${results.config.success ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = Object.values(results).every(r => r && r.success);
  console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
  
  if (allPassed) {
    console.log('\n🎉 重构成功！服务层功能完整性验证通过。');
  } else {
    console.log('\n⚠️ 重构存在问题，请检查失败的测试项。');
  }
  
  return results;
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
