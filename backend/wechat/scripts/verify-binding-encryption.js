#!/usr/bin/env node

/**
 * 微信绑定加密解密验证脚本
 * 用于验证加密解密过程的一致性，防止生产环境出现解密问题
 * 
 * 使用方法：
 * node verify-binding-encryption.js
 * 
 * 或者指定测试UUID：
 * node verify-binding-encryption.js e26f88c1-b8fb-4c4b-94c2-4dd885a49a3c
 */

require('dotenv').config({ path: '../../../.env' });
const crypto = require('crypto');

// 加密函数（复制自wechatApi.js）
function encryptBindingData(userUuid, bindingSecret) {
  const bindingData = {
    user_uuid: userUuid,
    timestamp: new Date().toISOString(),
    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };

  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(bindingSecret, 'salt', 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encryptedData = cipher.update(JSON.stringify(bindingData), 'utf8', 'base64');
  encryptedData += cipher.final('base64');

  const combined = iv.toString('base64') + ':' + encryptedData;
  
  return {
    combined,
    originalData: bindingData
  };
}

// 解密函数（复制自MessageProcessService.js）
function decryptBindingData(encryptedToken, bindingSecret) {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(bindingSecret, 'salt', 32);

  // 先进行URL解码
  const decodedToken = decodeURIComponent(encryptedToken);

  let decryptedData;

  if (decodedToken.includes(':')) {
    // 新格式（包含IV）
    const parts = decodedToken.split(':');
    if (parts.length !== 2) {
      throw new Error('新格式加密数据无效');
    }

    const iv = Buffer.from(parts[0], 'base64');
    const encryptedDataBase64 = parts[1];

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decryptedData = decipher.update(encryptedDataBase64, 'base64', 'utf8');
    decryptedData += decipher.final('utf8');
  } else {
    // 旧格式（兼容）
    const decipher = crypto.createDecipher('aes-256-cbc', bindingSecret);
    decryptedData = decipher.update(decodedToken, 'base64', 'utf8');
    decryptedData += decipher.final('utf8');
  }

  return JSON.parse(decryptedData);
}

// 主验证函数
function verifyEncryptionDecryption() {
  console.log('🔐 微信绑定加密解密验证开始\n');

  // 获取环境变量
  const bindingSecret = process.env.WECHAT_BINDING_SECRET || 'default_secret_key';
  console.log('密钥来源:', process.env.WECHAT_BINDING_SECRET ? '环境变量' : '默认值');
  console.log('密钥长度:', bindingSecret.length);
  console.log('密钥内容:', bindingSecret.substring(0, 10) + '...\n');

  // 测试UUID（从命令行参数获取或使用默认值）
  const testUuid = process.argv[2] || 'e26f88c1-b8fb-4c4b-94c2-4dd885a49a3c';
  console.log('测试UUID:', testUuid);

  // 验证UUID格式
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(testUuid)) {
    console.error('❌ 无效的UUID格式');
    process.exit(1);
  }

  console.log('✅ UUID格式验证通过\n');

  try {
    // 1. 测试加密
    console.log('🔒 开始加密测试...');
    const encryptResult = encryptBindingData(testUuid, bindingSecret);
    console.log('加密数据长度:', encryptResult.combined.length);
    console.log('加密数据前100字符:', encryptResult.combined.substring(0, 100));
    console.log('原始数据:', encryptResult.originalData);
    console.log('✅ 加密成功\n');

    // 2. 测试解密
    console.log('🔓 开始解密测试...');
    const decryptResult = decryptBindingData(encryptResult.combined, bindingSecret);
    console.log('解密结果:', decryptResult);
    console.log('✅ 解密成功\n');

    // 3. 验证数据一致性
    console.log('🔍 验证数据一致性...');
    if (decryptResult.user_uuid === testUuid) {
      console.log('✅ UUID一致性验证通过');
    } else {
      console.error('❌ UUID一致性验证失败');
      console.error('原始UUID:', testUuid);
      console.error('解密UUID:', decryptResult.user_uuid);
      process.exit(1);
    }

    if (decryptResult.timestamp === encryptResult.originalData.timestamp) {
      console.log('✅ 时间戳一致性验证通过');
    } else {
      console.error('❌ 时间戳一致性验证失败');
      process.exit(1);
    }

    // 4. 测试URL编码场景
    console.log('\n🌐 测试URL编码场景...');
    const urlEncoded = encodeURIComponent(encryptResult.combined);
    const urlDecryptResult = decryptBindingData(urlEncoded, bindingSecret);
    
    if (urlDecryptResult.user_uuid === testUuid) {
      console.log('✅ URL编码场景验证通过');
    } else {
      console.error('❌ URL编码场景验证失败');
      process.exit(1);
    }

    // 5. 测试多次加密解密
    console.log('\n🔄 测试多次加密解密（10次）...');
    for (let i = 0; i < 10; i++) {
      const multiEncrypt = encryptBindingData(testUuid, bindingSecret);
      const multiDecrypt = decryptBindingData(multiEncrypt.combined, bindingSecret);
      
      if (multiDecrypt.user_uuid !== testUuid) {
        console.error(`❌ 第${i + 1}次测试失败`);
        process.exit(1);
      }
    }
    console.log('✅ 多次测试全部通过');

    console.log('\n🎉 所有验证测试通过！加密解密过程正常工作。');

  } catch (error) {
    console.error('\n❌ 验证过程失败:', error.message);
    console.error('错误堆栈:', error.stack);
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  verifyEncryptionDecryption();
}

module.exports = {
  encryptBindingData,
  decryptBindingData,
  verifyEncryptionDecryption
}; 