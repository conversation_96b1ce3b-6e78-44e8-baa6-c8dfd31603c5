/**
 * 媒体下载服务
 * 专门处理媒体文件下载和管理
 */

const wechatApi = require('./wechatApi');
const { logInfo, logError } = require('../api/errorHandler');

/**
 * 获取企业微信访问令牌
 */
const getAccessToken = async () => {
  try {
    return await wechatApi.getAccessToken();
  } catch (error) {
    logError('获取access_token失败:', error);
    throw error;
  }
};

/**
 * 下载微信媒体文件
 */
const downloadWeChatMediaFile = async (mediaId, userUuid) => {
  try {
    console.log('📱 开始下载微信媒体文件:', { mediaId, userUuid });

    // 调用改进后的企业微信API下载媒体文件
    const downloadResult = await wechatApi.downloadMedia(mediaId);
    if (!downloadResult.success) {
      throw new Error('下载媒体文件失败: ' + downloadResult.message);
    }

    console.log('✅ 微信媒体文件下载成功:', {
      mediaId,
      fileName: downloadResult.fileName,
      contentType: downloadResult.contentType,
      fileExtension: downloadResult.fileExtension,
      size: downloadResult.size
    });

    // 返回完整的文件信息，包括智能检测的文件类型
    return {
      success: true,
      data: downloadResult.data,
      fileName: downloadResult.fileName,
      contentType: downloadResult.contentType,
      fileExtension: downloadResult.fileExtension,
      size: downloadResult.size,
      originalContentType: downloadResult.originalContentType
    };
  } catch (error) {
    logError('下载微信媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 批量下载媒体文件
 */
const batchDownloadMediaFiles = async (mediaIds, userUuid) => {
  try {
    logInfo(`📥 开始批量下载 ${mediaIds.length} 个媒体文件`);
    
    const results = [];
    const concurrency = 3; // 并发下载数量限制
    
    for (let i = 0; i < mediaIds.length; i += concurrency) {
      const batch = mediaIds.slice(i, i + concurrency);
      const batchPromises = batch.map(mediaId => 
        downloadWeChatMediaFile(mediaId, userUuid)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map((result, index) => ({
        mediaId: batch[index],
        success: result.status === 'fulfilled' && result.value.success,
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason.message : 
               (result.value && !result.value.success ? result.value.message : null)
      })));
    }
    
    const successCount = results.filter(r => r.success).length;
    logInfo(`✅ 批量下载完成: ${successCount}/${mediaIds.length} 成功`);
    
    return {
      success: true,
      results,
      successCount,
      totalCount: mediaIds.length
    };
  } catch (error) {
    logError('批量下载媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取媒体文件信息（不下载文件内容）
 */
const getMediaFileInfo = async (mediaId) => {
  try {
    const result = await wechatApi.getMediaInfo(mediaId);
    if (result.success) {
      return {
        success: true,
        mediaId,
        contentType: result.contentType,
        size: result.size,
        fileName: result.fileName
      };
    } else {
      return {
        success: false,
        message: result.message
      };
    }
  } catch (error) {
    logError('获取媒体文件信息失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 验证媒体文件是否可下载
 */
const validateMediaAccess = async (mediaId, userUuid) => {
  try {
    // 检查媒体ID格式
    if (!mediaId || typeof mediaId !== 'string') {
      return {
        success: false,
        message: '无效的媒体ID'
      };
    }
    
    // 检查用户权限（这里可以添加更多的权限验证逻辑）
    if (!userUuid) {
      return {
        success: false,
        message: '用户身份验证失败'
      };
    }
    
    // 尝试获取媒体文件信息来验证访问权限
    const infoResult = await getMediaFileInfo(mediaId);
    if (!infoResult.success) {
      return {
        success: false,
        message: '媒体文件不存在或已过期'
      };
    }
    
    return {
      success: true,
      message: '媒体文件访问验证通过',
      fileInfo: infoResult
    };
  } catch (error) {
    logError('验证媒体文件访问权限失败:', error);
    return {
      success: false,
      message: '访问权限验证失败'
    };
  }
};

/**
 * 清理过期的媒体文件缓存
 */
const cleanupExpiredMediaCache = async () => {
  try {
    logInfo('🧹 开始清理过期媒体文件缓存');
    
    // 这里可以添加清理本地缓存文件的逻辑
    // 例如删除超过72小时的临时文件
    
    const fileService = require('./fileService');
    const cleanupResult = await fileService.cleanupExpiredFiles();
    
    if (cleanupResult.success) {
      logInfo(`✅ 媒体文件缓存清理完成，删除了 ${cleanupResult.deletedCount} 个文件`);
      return {
        success: true,
        deletedCount: cleanupResult.deletedCount
      };
    } else {
      return {
        success: false,
        message: '缓存清理失败'
      };
    }
  } catch (error) {
    logError('清理媒体文件缓存失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取媒体下载统计信息
 */
const getDownloadStats = async (userUuid, timeRange = '24h') => {
  try {
    const db = require('../data/database');
    
    // 根据时间范围计算开始时间
    let startTime;
    switch (timeRange) {
      case '1h':
        startTime = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }
    
    const stats = await db.getMediaDownloadStats(userUuid, startTime);
    
    return {
      success: true,
      stats: {
        totalDownloads: stats.total_downloads || 0,
        totalSize: stats.total_size || 0,
        averageSize: stats.average_size || 0,
        timeRange,
        startTime: startTime.toISOString()
      }
    };
  } catch (error) {
    logError('获取下载统计信息失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 预热媒体文件（预先下载到缓存）
 */
const preloadMediaFiles = async (mediaIds, userUuid) => {
  try {
    logInfo(`🔥 开始预热 ${mediaIds.length} 个媒体文件`);
    
    // 使用较低的并发数进行预热下载
    const batchResult = await batchDownloadMediaFiles(mediaIds, userUuid);
    
    if (batchResult.success) {
      logInfo(`✅ 媒体文件预热完成: ${batchResult.successCount}/${batchResult.totalCount}`);
      return {
        success: true,
        preloadedCount: batchResult.successCount,
        totalCount: batchResult.totalCount
      };
    } else {
      return {
        success: false,
        message: '媒体文件预热失败'
      };
    }
  } catch (error) {
    logError('预热媒体文件失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

module.exports = {
  getAccessToken,
  downloadWeChatMediaFile,
  batchDownloadMediaFiles,
  getMediaFileInfo,
  validateMediaAccess,
  cleanupExpiredMediaCache,
  getDownloadStats,
  preloadMediaFiles
};
