/**
 * 消息同步服务
 * 专门处理消息同步和设备管理
 */

const db = require('../data/database');
const { logInfo, logError } = require('../api/errorHandler');

/**
 * 保存消息元数据（不存储内容）
 */
const saveMessageMetadata = async (messageData) => {
  try {
    const result = await db.saveMessageMetadata(messageData);
    if (result.success) {
      return {
        success: true,
        message_id: result.message_id,
        media_id_expires_at: messageData.media_id_expires_at
      };
    } else {
      return {
        success: false,
        message: '消息元数据保存失败'
      };
    }
  } catch (error) {
    logError('保存消息元数据异常:', error);
    return {
      success: false,
      message: '消息元数据保存异常: ' + error.message
    };
  }
};

/**
 * 推送消息到用户所有设备
 */
const pushMessageToDevices = async (userUuid, messageId) => {
  try {
    const result = await db.pushMessageToDevices(userUuid, messageId);
    if (result.success) {
      return {
        success: true,
        message: '消息推送成功'
      };
    } else {
      return {
        success: false,
        message: '消息推送失败'
      };
    }
  } catch (error) {
    logError('推送消息到设备异常:', error);
    return {
      success: false,
      message: '消息推送异常: ' + error.message
    };
  }
};

/**
 * 推送下载指令到用户所有设备
 */
const pushDownloadInstructionToDevices = async (userUuid, downloadInstruction) => {
  try {
    const result = await db.pushDownloadInstructionToDevices(userUuid, downloadInstruction);
    if (result.success) {
      return {
        success: true,
        message: '下载指令推送成功'
      };
    } else {
      return {
        success: false,
        message: '下载指令推送失败'
      };
    }
  } catch (error) {
    logError('推送下载指令异常:', error);
    return {
      success: false,
      message: '下载指令推送异常: ' + error.message
    };
  }
};

/**
 * 获取增量消息
 */
const getIncrementalMessages = async (userUuid, deviceId, sinceId, limit) => {
  try {
    const result = await db.getIncrementalMessages(userUuid, deviceId, sinceId, limit);
    if (result.success) {
      return {
        success: true,
        messages: result.messages,
        has_more: result.has_more,
        next_since_id: result.next_since_id
      };
    } else {
      return {
        success: false,
        message: '获取增量消息失败'
      };
    }
  } catch (error) {
    logError('获取增量消息异常:', error);
    return {
      success: false,
      message: '获取增量消息异常: ' + error.message
    };
  }
};

/**
 * 更新设备同步状态
 */
const updateDeviceSyncStatus = async (userUuid, deviceId, lastSyncedId) => {
  try {
    const result = await db.updateDeviceSyncStatus(userUuid, deviceId, lastSyncedId);
    if (result.success) {
      return {
        success: true,
        message: '设备同步状态更新成功'
      };
    } else {
      return {
        success: false,
        message: '设备同步状态更新失败'
      };
    }
  } catch (error) {
    logError('更新设备同步状态异常:', error);
    return {
      success: false,
      message: '设备同步状态更新异常: ' + error.message
    };
  }
};

/**
 * 注册设备
 */
const registerDevice = async (deviceInfo) => {
  try {
    // 为可选参数提供默认值，防止数据库绑定错误
    const safeDeviceInfo = {
      ...deviceInfo,
      device_name: deviceInfo.device_name || null,
      app_version: deviceInfo.app_version || null,
      push_token: deviceInfo.push_token || null
    };

    const result = await db.registerDevice(safeDeviceInfo);
    if (result.success) {
      return {
        success: true,
        device: result.device,
        message: '设备注册成功'
      };
    } else {
      return {
        success: false,
        message: '设备注册失败'
      };
    }
  } catch (error) {
    logError('注册设备异常:', error);
    return {
      success: false,
      message: '设备注册异常: ' + error.message
    };
  }
};

/**
 * 获取短期访问令牌（用于客户端下载媒体文件）
 */
const getShortTermAccessToken = async (userUuid) => {
  try {
    const result = await db.getShortTermAccessToken(userUuid);
    if (result.success) {
      return {
        success: true,
        access_token: result.access_token,
        expires_in: result.expires_in
      };
    } else {
      return {
        success: false,
        message: '获取短期访问令牌失败'
      };
    }
  } catch (error) {
    logError('获取短期访问令牌异常:', error);
    return {
      success: false,
      message: '获取短期访问令牌异常: ' + error.message
    };
  }
};

/**
 * 处理已绑定用户的文本消息
 */
const processUserTextMessage = async (binding, message) => {
  try {
    // 记录消息到数据库（仅元数据）
    const messageRecord = await saveMessageMetadata({
      user_uuid: binding.user_uuid,
      external_userid: binding.external_userid,
      wechat_message_id: message.MsgId,
      message_type: 'text',
      content: message.Content,
      metadata: {
        timestamp: message.CreateTime,
        from_user: message.FromUserName,
        to_user: message.ToUserName
      }
    });

    if (messageRecord.success) {
      // 推送到用户所有设备
      await pushMessageToDevices(binding.user_uuid, messageRecord.message_id);

      return {
        success: true,
        message: '文本消息已处理并推送到设备'
      };
    } else {
      return {
        success: false,
        message: '消息记录失败'
      };
    }
  } catch (error) {
    logError('处理用户文本消息异常:', error);
    return {
      success: false,
      message: '文本消息处理失败'
    };
  }
};

/**
 * 批量获取用户消息
 */
const getUserMessages = async (userUuid, limit = 50, offset = 0) => {
  try {
    const result = await db.getUserMessages(userUuid, limit, offset);
    if (result.success) {
      return {
        success: true,
        messages: result.messages,
        total: result.total,
        has_more: result.has_more
      };
    } else {
      return {
        success: false,
        message: '获取用户消息失败'
      };
    }
  } catch (error) {
    logError('获取用户消息异常:', error);
    return {
      success: false,
      message: '获取用户消息异常: ' + error.message
    };
  }
};

/**
 * 标记消息为已读
 */
const markMessageAsRead = async (userUuid, messageId) => {
  try {
    const result = await db.markMessageAsRead(userUuid, messageId);
    if (result.success) {
      return {
        success: true,
        message: '消息已标记为已读'
      };
    } else {
      return {
        success: false,
        message: '标记消息已读失败'
      };
    }
  } catch (error) {
    logError('标记消息已读异常:', error);
    return {
      success: false,
      message: '标记消息已读异常: ' + error.message
    };
  }
};

/**
 * 删除过期消息
 */
const cleanupExpiredMessages = async () => {
  try {
    const result = await db.cleanupExpiredMessages();
    if (result.success) {
      logInfo(`✅ 清理过期消息完成，删除了 ${result.deletedCount} 条消息`);
      return {
        success: true,
        deletedCount: result.deletedCount
      };
    } else {
      return {
        success: false,
        message: '清理过期消息失败'
      };
    }
  } catch (error) {
    logError('清理过期消息异常:', error);
    return {
      success: false,
      message: '清理过期消息异常: ' + error.message
    };
  }
};

module.exports = {
  saveMessageMetadata,
  pushMessageToDevices,
  pushDownloadInstructionToDevices,
  getIncrementalMessages,
  updateDeviceSyncStatus,
  registerDevice,
  getShortTermAccessToken,
  processUserTextMessage,
  getUserMessages,
  markMessageAsRead,
  cleanupExpiredMessages
};
