/**
 * 用户绑定服务
 * 专门处理用户绑定相关逻辑
 */

const crypto = require('crypto');
const CryptoJS = require('crypto-js');
const db = require('../data/database');
const wechatApi = require('./wechatApi');
const { logInfo, logError } = require('../api/errorHandler');

/**
 * 根据用户UUID查询绑定信息
 */
const getBindingByUserUuid = async (userUuid) => {
  try {
    const binding = await db.getBindingByUserUuid(userUuid);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据用户UUID查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 根据external_userid查询绑定信息
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    const binding = await db.getBindingByExternalUserId(externalUserId);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据external_userid查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 解除用户绑定
 */
const unbindUser = async (userUuid) => {
  try {
    logInfo('🔓 开始解除用户绑定:', userUuid);

    // 1. 检查用户是否已绑定
    const existingBinding = await getBindingByUserUuid(userUuid);
    if (!existingBinding || existingBinding.binding_status !== 'active') {
      return {
        success: false,
        message: '用户未绑定或绑定状态异常'
      };
    }

    // 2. 删除绑定关系
    const result = await db.deleteBinding(userUuid);
    if (result.success) {
      logInfo('✅ 用户绑定解除成功:', userUuid);
      return {
        success: true,
        message: '用户绑定解除成功'
      };
    } else {
      return {
        success: false,
        message: '绑定删除失败'
      };
    }
  } catch (error) {
    logError('解除用户绑定异常:', error);
    return {
      success: false,
      message: '解除用户绑定失败: ' + error.message
    };
  }
};

/**
 * 检查是否为加密绑定令牌
 */
const isEncryptedBindingToken = async (content) => {
  try {
    logInfo('检查是否为绑定令牌:', content ? content.substring(0, 20) + '...' : 'empty');

    // 简单的格式检查：加密令牌通常是Base64编码的长字符串
    if (!content || content.length < 32) {
      return false;
    }

    // 检查是否为Base64格式
    const base64Regex = /^[A-Za-z0-9+/]+=*$/;
    if (!base64Regex.test(content)) {
      return false;
    }

    // 尝试解密验证
    try {
      const decryptResult = await decryptBindingToken(content);
      return decryptResult.success;
    } catch (error) {
      return false;
    }
  } catch (error) {
    logError('检查绑定令牌异常:', error);
    return false;
  }
};

/**
 * 解密绑定令牌
 */
const decryptBindingToken = async (encryptedToken) => {
  try {
    const WECHAT_BINDING_TOKEN_SECRET = process.env.WECHAT_BINDING_TOKEN_SECRET;
    if (!WECHAT_BINDING_TOKEN_SECRET) {
      throw new Error('WECHAT_BINDING_TOKEN_SECRET environment variable is required');
    }

    // 使用AES解密
    const bytes = CryptoJS.AES.decrypt(encryptedToken, WECHAT_BINDING_TOKEN_SECRET);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);

    if (!decryptedData) {
      throw new Error('解密结果为空');
    }

    // 解析JSON数据
    const tokenData = JSON.parse(decryptedData);
    
    return {
      success: true,
      data: tokenData
    };
  } catch (error) {
    logError('解密绑定令牌失败:', error);
    return {
      success: false,
      message: '绑定令牌解密失败: ' + error.message
    };
  }
};

/**
 * 处理绑定令牌
 */
const processBindingToken = async (externalUserId, encryptedToken) => {
  try {
    logInfo('🔗 开始处理绑定令牌');
    logInfo('外部用户ID:', externalUserId ? externalUserId.substring(0, 10) + '...' : 'undefined');
    logInfo('加密令牌长度:', encryptedToken ? encryptedToken.length : 0);
    logInfo('加密令牌前50字符:', encryptedToken ? encryptedToken.substring(0, 50) + '...' : 'undefined');

    // 基本参数验证
    if (!externalUserId || !encryptedToken) {
      logError('❌ 绑定令牌处理失败：缺少必要参数');
      return {
        success: false,
        message: '绑定参数不完整'
      };
    }

    // 验证external_userid格式
    if (typeof externalUserId !== 'string' || externalUserId.length < 10) {
      logError('❌ 无效的external_userid格式:', externalUserId);
      return {
        success: false,
        message: '无效的微信用户标识'
      };
    }

    // 验证加密令牌基本格式
    if (typeof encryptedToken !== 'string' || encryptedToken.length < 32) {
      logError('❌ 无效的绑定令牌格式');
      return {
        success: false,
        message: '绑定令牌格式无效'
      };
    }

    // 解密绑定令牌
    const decryptResult = await decryptBindingToken(encryptedToken);
    if (!decryptResult.success) {
      logError('❌ 绑定令牌解密失败:', decryptResult.message);
      return {
        success: false,
        message: '绑定令牌无效或已过期，请重新生成绑定链接'
      };
    }

    const tokenData = decryptResult.data;
    const userUuid = tokenData.user_uuid;
    const timestamp = tokenData.timestamp;

    // 严格验证解密后的数据
    if (!userUuid || !timestamp) {
      logError('❌ 解密后的令牌数据不完整');
      logError('数据内容:', tokenData);
      return {
        success: false,
        message: '绑定令牌数据损坏'
      };
    }

    // 验证UUID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userUuid)) {
      logError('❌ 解密后的UUID格式无效:', userUuid);
      return {
        success: false,
        message: '绑定令牌包含无效的用户标识'
      };
    }

    // 验证时间戳格式
    const timestampDate = new Date(timestamp);
    if (isNaN(timestampDate.getTime())) {
      logError('❌ 解密后的时间戳格式无效:', timestamp);
      return {
        success: false,
        message: '绑定令牌时间戳无效'
      };
    }

    logInfo('✅ 令牌解密和验证成功');
    logInfo('解密后的令牌数据:', {
      user_uuid: userUuid,
      timestamp: timestampDate.toISOString(),
      legacy_format: tokenData.legacy_format || false
    });

    // 检查令牌是否过期（24小时有效期）
    const now = Date.now();
    const tokenAge = now - timestampDate.getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    if (tokenAge > maxAge) {
      logError('❌ 绑定令牌已过期');
      logError('令牌时间:', timestampDate.toISOString());
      logError('当前时间:', new Date(now).toISOString());
      logError('过期时长:', Math.round(tokenAge / 1000 / 60), '分钟');
      return {
        success: false,
        message: '绑定令牌已过期，请重新生成绑定链接'
      };
    }

    logInfo('✅ 令牌时效性验证通过');

    // 检查用户是否已经绑定了其他微信账号
    const existingBinding = await getBindingByUserUuid(userUuid);
    if (existingBinding && existingBinding.binding_status === 'active') {
      logInfo('⚠️ 用户已绑定其他微信账号，执行更新绑定');
      logInfo('原绑定external_userid:', existingBinding.external_userid);
      logInfo('新绑定external_userid:', externalUserId);
      
      // 更新绑定到新的微信账号
      const updateResult = await db.updateUserBinding(userUuid, externalUserId);
      if (updateResult.success) {
        logInfo('✅ 用户绑定更新成功');
        
        // 推送绑定成功通知到用户所有设备
        await pushBindingSuccessToDevices(userUuid);
        
        return {
          success: true,
          message: '绑定更新成功',
          binding_type: 'update'
        };
      } else {
        logError('❌ 绑定更新失败:', updateResult.message);
        return {
          success: false,
          message: '绑定更新失败，请重试'
        };
      }
    }

    // 检查微信账号是否已经绑定了其他用户
    const existingWeChatBinding = await getBindingByExternalUserId(externalUserId);
    if (existingWeChatBinding && existingWeChatBinding.binding_status === 'active') {
      logError('❌ 该微信账号已绑定其他用户');
      logError('已绑定的用户UUID:', existingWeChatBinding.user_uuid);
      return {
        success: false,
        message: '该微信账号已绑定其他用户，请使用其他微信账号'
      };
    }

    // 创建新的绑定关系
    logInfo('📝 创建新的绑定关系');
    const bindingResult = await db.createUserBinding({
      user_uuid: userUuid,
      external_userid: externalUserId,
      binding_status: 'active',
      binding_time: new Date(),
      binding_method: 'token',
      token_metadata: {
        token_timestamp: timestamp,
        binding_timestamp: new Date().toISOString(),
        legacy_format: tokenData.legacy_format || false
      }
    });

    if (bindingResult.success) {
      logInfo('✅ 用户绑定创建成功');
      logInfo('绑定详情:', {
        user_uuid: userUuid,
        external_userid: externalUserId,
        binding_time: new Date().toISOString()
      });
      
      // 推送绑定成功通知到用户所有设备
      await pushBindingSuccessToDevices(userUuid);
      
      return {
        success: true,
        message: '绑定成功',
        binding_type: 'create',
        data: {
          user_uuid: userUuid,
          external_userid: externalUserId,
          binding_time: new Date().toISOString()
        }
      };
    } else {
      logError('❌ 绑定创建失败:', bindingResult.message);
      return {
        success: false,
        message: '绑定创建失败，请重试'
      };
    }
  } catch (error) {
    logError('❌ 处理绑定令牌异常:', error);
    logError('错误堆栈:', error.stack);
    return {
      success: false,
      message: '绑定处理失败，请重新生成绑定链接并重试'
    };
  }
};

/**
 * 推送绑定成功通知到用户所有设备
 */
const pushBindingSuccessToDevices = async (userUuid) => {
  try {
    // 获取用户所有活跃设备
    const devices = await db.query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND status = 'active'
    `, [userUuid]);

    if (devices.length === 0) {
      logInfo('用户没有活跃设备，跳过推送通知');
      return { success: true };
    }

    // 推送绑定成功通知
    const pushService = require('./pushService');
    for (const device of devices) {
      if (device.push_token) {
        await pushService.sendPushNotification(device.push_token, {
          title: '微信绑定成功',
          body: '您的微信账号已成功绑定到公职猫',
          data: {
            type: 'binding_success',
            user_uuid: userUuid
          }
        }, device.platform);
      }
    }

    return { success: true };
  } catch (error) {
    logError('推送绑定成功通知失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 生成微信客服绑定链接
 */
const generateWeChatBindingLink = async (userUuid, openKfId = null, scene = 'binding') => {
  try {
    logInfo('🔗 开始生成微信客服绑定链接:', { userUuid, openKfId, scene });

    // 如果没有指定客服账号ID，使用环境变量中的默认值
    const kfId = openKfId || process.env.WECHAT_DEFAULT_OPEN_KFID;
    if (!kfId) {
      throw new Error('缺少微信客服账号ID配置');
    }

    // 生成绑定链接
    const linkResult = await wechatApi.generateBindingKfLink(kfId, userUuid, scene);
    
    if (linkResult.success) {
      return {
        success: true,
        binding_url: linkResult.binding_url
      };
    } else {
      return {
        success: false,
        message: linkResult.message
      };
    }
  } catch (error) {
    logError('生成微信客服绑定链接失败:', error);
    return {
      success: false,
      message: '生成绑定链接失败: ' + error.message
    };
  }
};

module.exports = {
  getBindingByUserUuid,
  getBindingByExternalUserId,
  unbindUser,
  isEncryptedBindingToken,
  decryptBindingToken,
  processBindingToken,
  pushBindingSuccessToDevices,
  generateWeChatBindingLink
};
