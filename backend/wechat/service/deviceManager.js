// 设备类型管理模块
// 支持多种设备类型的识别、验证和特殊处理

/**
 * 支持的设备类型配置
 */
const DEVICE_TYPES = {
  // 移动设备
  'ios': {
    name: 'iOS',
    category: 'mobile',
    push_provider: 'apns',
    supported_versions: ['13.0', '14.0', '15.0', '16.0', '17.0', '18.0'],
    features: ['push_notification', 'background_sync', 'biometric_auth']
  },
  'android': {
    name: 'Android',
    category: 'mobile',
    push_provider: 'fcm',
    supported_versions: ['8.0', '9.0', '10.0', '11.0', '12.0', '13.0', '14.0', '15.0'],
    features: ['push_notification', 'background_sync', 'biometric_auth']
  },
  'harmonyos': {
    name: 'HarmonyOS',
    category: 'mobile',
    push_provider: 'hms',
    supported_versions: ['2.0', '3.0', '4.0', '5.0'],
    features: ['push_notification', 'background_sync', 'distributed_sync']
  },
  
  // 桌面设备
  'desktop': {
    name: 'Desktop',
    category: 'desktop',
    push_provider: 'jpush',
    supported_versions: ['1.0'],
    features: ['push_notification', 'file_sync', 'always_on']
  },
  'windows': {
    name: 'Windows',
    category: 'desktop',
    push_provider: 'wns',
    supported_versions: ['10', '11'],
    features: ['push_notification', 'file_sync', 'always_on']
  },
  'macos': {
    name: 'macOS',
    category: 'desktop',
    push_provider: 'apns',
    supported_versions: ['10.15', '11.0', '12.0', '13.0', '14.0', '15.0'],
    features: ['push_notification', 'file_sync', 'always_on']
  },
  'linux': {
    name: 'Linux',
    category: 'desktop',
    push_provider: 'jpush',
    supported_versions: ['1.0'],
    features: ['push_notification', 'file_sync']
  },
  
  // Web设备
  'web': {
    name: 'Web Browser',
    category: 'web',
    push_provider: 'web_push',
    supported_versions: ['1.0'],
    features: ['push_notification', 'limited_sync']
  },
  
  // 未来设备类型
  'hongmeng': {
    name: 'HongMeng OS',
    category: 'mobile',
    push_provider: 'hms',
    supported_versions: ['1.0', '2.0'],
    features: ['push_notification', 'background_sync', 'distributed_sync']
  },
  'wearos': {
    name: 'Wear OS',
    category: 'wearable',
    push_provider: 'fcm',
    supported_versions: ['2.0', '3.0', '4.0'],
    features: ['push_notification', 'limited_sync']
  },
  'watchos': {
    name: 'watchOS',
    category: 'wearable',
    push_provider: 'apns',
    supported_versions: ['7.0', '8.0', '9.0', '10.0', '11.0'],
    features: ['push_notification', 'limited_sync']
  },
  'iot': {
    name: 'IoT Device',
    category: 'iot',
    push_provider: 'mqtt',
    supported_versions: ['1.0'],
    features: ['basic_sync']
  }
};

/**
 * 验证设备类型
 * @param {string} platform 设备平台
 * @returns {boolean} 是否支持
 */
const isValidPlatform = (platform) => {
  return platform && DEVICE_TYPES.hasOwnProperty(platform.toLowerCase());
};

/**
 * 获取设备类型信息
 * @param {string} platform 设备平台
 * @returns {Object|null} 设备类型信息
 */
const getDeviceTypeInfo = (platform) => {
  if (!platform) return null;
  return DEVICE_TYPES[platform.toLowerCase()] || null;
};

/**
 * 获取设备推送提供商
 * @param {string} platform 设备平台
 * @returns {string} 推送提供商
 */
const getPushProvider = (platform) => {
  const deviceInfo = getDeviceTypeInfo(platform);
  return deviceInfo ? deviceInfo.push_provider : 'jpush';
};

/**
 * 检查设备是否支持特定功能
 * @param {string} platform 设备平台
 * @param {string} feature 功能名称
 * @returns {boolean} 是否支持
 */
const supportsFeature = (platform, feature) => {
  const deviceInfo = getDeviceTypeInfo(platform);
  return deviceInfo ? deviceInfo.features.includes(feature) : false;
};

/**
 * 验证设备版本
 * @param {string} platform 设备平台
 * @param {string} version 版本号
 * @returns {boolean} 版本是否支持
 */
const isVersionSupported = (platform, version) => {
  const deviceInfo = getDeviceTypeInfo(platform);
  if (!deviceInfo || !version) return false;
  
  // 对于支持的版本列表，检查是否包含
  return deviceInfo.supported_versions.includes(version) || 
         deviceInfo.supported_versions.includes('1.0'); // 默认支持
};

/**
 * 生成设备指纹
 * @param {Object} deviceInfo 设备信息
 * @returns {string} 设备指纹
 */
const generateDeviceFingerprint = (deviceInfo) => {
  const {
    platform,
    platform_version,
    device_model,
    app_version,
    device_id
  } = deviceInfo;
  
  const crypto = require('crypto');
  // 处理undefined值，避免生成包含"undefined"字符串的指纹
  const fingerprintData = [
    platform || '',
    platform_version || '',
    device_model || '',
    app_version || '',
    device_id || ''
  ].join('|');
  
  return crypto.createHash('sha256').update(fingerprintData).digest('hex');
};

/**
 * 验证设备信息完整性
 * @param {Object} deviceInfo 设备信息
 * @returns {Object} 验证结果
 */
const validateDeviceInfo = (deviceInfo) => {
  const errors = [];
  
  // 必需字段检查
  if (!deviceInfo.platform) {
    errors.push('平台信息不能为空');
  } else if (!isValidPlatform(deviceInfo.platform)) {
    errors.push(`不支持的设备平台: ${deviceInfo.platform}`);
  }
  
  if (!deviceInfo.device_id) {
    errors.push('设备ID不能为空');
  }
  
  if (!deviceInfo.app_version) {
    errors.push('应用版本不能为空');
  }
  
  // 版本支持检查
  if (deviceInfo.platform && deviceInfo.platform_version) {
    if (!isVersionSupported(deviceInfo.platform, deviceInfo.platform_version)) {
      errors.push(`不支持的平台版本: ${deviceInfo.platform} ${deviceInfo.platform_version}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 标准化设备信息
 * @param {Object} deviceInfo 原始设备信息
 * @returns {Object} 标准化后的设备信息
 */
const normalizeDeviceInfo = (deviceInfo) => {
  const platform = deviceInfo.platform ? deviceInfo.platform.toLowerCase() : '';
  const deviceTypeInfo = getDeviceTypeInfo(platform);
  
  return {
    user_uuid: deviceInfo.user_uuid,
    device_id: deviceInfo.device_id,
    device_name: deviceInfo.device_name || `${deviceTypeInfo?.name || platform} 设备`,
    platform: platform,
    platform_version: deviceInfo.platform_version,
    app_version: deviceInfo.app_version,
    device_model: deviceInfo.device_model,
    push_token: deviceInfo.push_token,
    push_provider: getPushProvider(platform),
    device_fingerprint: generateDeviceFingerprint(deviceInfo),
    device_status: 'active'
  };
};

/**
 * 获取设备同步策略
 * @param {string} platform 设备平台
 * @returns {Object} 同步策略
 */
const getSyncStrategy = (platform) => {
  const deviceInfo = getDeviceTypeInfo(platform);
  
  if (!deviceInfo) {
    return {
      sync_interval: 300, // 5分钟
      batch_size: 50,
      supports_background: false
    };
  }
  
  switch (deviceInfo.category) {
    case 'mobile':
      return {
        sync_interval: 60, // 1分钟
        batch_size: 100,
        supports_background: supportsFeature(platform, 'background_sync')
      };
    case 'desktop':
      return {
        sync_interval: 30, // 30秒
        batch_size: 200,
        supports_background: true
      };
    case 'web':
      return {
        sync_interval: 120, // 2分钟
        batch_size: 50,
        supports_background: false
      };
    case 'wearable':
      return {
        sync_interval: 300, // 5分钟
        batch_size: 20,
        supports_background: false
      };
    case 'iot':
      return {
        sync_interval: 600, // 10分钟
        batch_size: 10,
        supports_background: false
      };
    default:
      return {
        sync_interval: 300,
        batch_size: 50,
        supports_background: false
      };
  }
};

/**
 * 获取所有支持的设备类型
 * @returns {Array} 设备类型列表
 */
const getSupportedDeviceTypes = () => {
  return Object.keys(DEVICE_TYPES).map(platform => ({
    platform,
    ...DEVICE_TYPES[platform]
  }));
};

/**
 * 根据User-Agent识别设备类型
 * @param {string} userAgent User-Agent字符串
 * @returns {Object} 识别结果
 */
const detectDeviceFromUserAgent = (userAgent) => {
  if (!userAgent) return null;
  
  const ua = userAgent.toLowerCase();
  
  // iOS设备
  if (ua.includes('iphone') || ua.includes('ipad') || ua.includes('ipod')) {
    return {
      platform: 'ios',
      category: 'mobile'
    };
  }
  
  // Android设备
  if (ua.includes('android')) {
    return {
      platform: 'android',
      category: 'mobile'
    };
  }
  
  // HarmonyOS设备
  if (ua.includes('harmonyos') || ua.includes('hongmeng')) {
    return {
      platform: 'harmonyos',
      category: 'mobile'
    };
  }
  
  // 桌面设备
  if (ua.includes('windows')) {
    return {
      platform: 'windows',
      category: 'desktop'
    };
  }
  
  if (ua.includes('macintosh') || ua.includes('mac os')) {
    return {
      platform: 'macos',
      category: 'desktop'
    };
  }
  
  if (ua.includes('linux')) {
    return {
      platform: 'linux',
      category: 'desktop'
    };
  }
  
  // 默认为web
  return {
    platform: 'web',
    category: 'web'
  };
};

module.exports = {
  DEVICE_TYPES,
  isValidPlatform,
  getDeviceTypeInfo,
  getPushProvider,
  supportsFeature,
  isVersionSupported,
  generateDeviceFingerprint,
  validateDeviceInfo,
  normalizeDeviceInfo,
  getSyncStrategy,
  getSupportedDeviceTypes,
  detectDeviceFromUserAgent
}; 