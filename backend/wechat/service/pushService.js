// 推送服务模块
// 支持多种推送提供商的统一推送服务

const axios = require('axios');
const deviceManager = require('./deviceManager');

// JPush配置
const JPUSH_CONFIG = {
  app_key: process.env.JPUSH_APP_KEY,
  master_secret: process.env.JPUSH_MASTER_SECRET,
  api_url: 'https://api.jpush.cn/v3/push'
};

// 推送提供商配置
const PUSH_PROVIDERS = {
  jpush: {
    name: 'JPush',
    api_url: 'https://api.jpush.cn/v3/push',
    auth_header: 'Basic'
  },
  apns: {
    name: 'Apple Push Notification Service',
    api_url: 'https://api.push.apple.com',
    auth_header: 'Bearer'
  },
  fcm: {
    name: 'Firebase Cloud Messaging',
    api_url: 'https://fcm.googleapis.com/fcm/send',
    auth_header: 'Bearer'
  },
  hms: {
    name: 'Huawei Mobile Services',
    api_url: 'https://push-api.cloud.huawei.com',
    auth_header: 'Bearer'
  },
  web_push: {
    name: 'Web Push',
    api_url: 'https://fcm.googleapis.com/fcm/send',
    auth_header: 'Bearer'
  }
};

/**
 * 生成JPush认证头
 * @returns {string} Base64编码的认证字符串
 */
const generateJPushAuth = () => {
  const credentials = `${JPUSH_CONFIG.app_key}:${JPUSH_CONFIG.master_secret}`;
  return Buffer.from(credentials).toString('base64');
};

/**
 * 通过JPush发送推送
 * @param {Array} devices 设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendJPushNotification = async (devices, message) => {
  try {
    const { title, content, data } = message;
    
    // 按平台分组设备
    const platformGroups = {
      ios: devices.filter(d => d.platform === 'ios'),
      android: devices.filter(d => d.platform === 'android'),
      other: devices.filter(d => !['ios', 'android'].includes(d.platform))
    };
    
    const pushData = {
      platform: ['ios', 'android'],
      audience: {
        registration_id: devices.map(d => d.push_token).filter(Boolean)
      },
      notification: {
        alert: content,
        android: {
          title: title,
          alert: content,
          extras: data
        },
        ios: {
          alert: content,
          badge: 1,
          sound: 'default',
          extras: data
        }
      },
      message: {
        msg_content: content,
        title: title,
        extras: data
      },
      options: {
        time_to_live: 3600,
        apns_production: process.env.NODE_ENV === 'production'
      }
    };
    
    const response = await axios.post(JPUSH_CONFIG.api_url, pushData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${generateJPushAuth()}`
      },
      timeout: 10000
    });
    
    if (response.status === 200) {
      return {
        success: true,
        message_id: response.data.msg_id,
        sent_count: devices.length,
        provider: 'jpush'
      };
    } else {
      throw new Error(`JPush推送失败: ${response.data.error?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('❌ JPush推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      provider: 'jpush'
    };
  }
};

/**
 * 通过APNS发送推送
 * @param {Array} devices iOS设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendAPNSNotification = async (devices, message) => {
  try {
    const { title, content, data } = message;
    
    // 这里应该实现真正的APNS推送逻辑
    // 需要使用Apple的推送证书或JWT令牌
    
    console.log('📱 APNS推送:', {
      devices: devices.length,
      message: { title, content }
    });
    
    // 模拟推送结果
    return {
      success: true,
      message_id: `apns_${Date.now()}`,
      sent_count: devices.length,
      provider: 'apns'
    };
  } catch (error) {
    console.error('❌ APNS推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      provider: 'apns'
    };
  }
};

/**
 * 通过FCM发送推送
 * @param {Array} devices Android设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendFCMNotification = async (devices, message) => {
  try {
    const { title, content, data } = message;
    
    const fcmData = {
      registration_ids: devices.map(d => d.push_token).filter(Boolean),
      notification: {
        title: title,
        body: content,
        icon: 'ic_notification',
        sound: 'default'
      },
      data: data,
      priority: 'high',
      time_to_live: 3600
    };
    
    const response = await axios.post(PUSH_PROVIDERS.fcm.api_url, fcmData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `key=${process.env.FCM_SERVER_KEY}`
      },
      timeout: 10000
    });
    
    if (response.status === 200) {
      return {
        success: true,
        message_id: response.data.multicast_id,
        sent_count: response.data.success || 0,
        failed_count: response.data.failure || 0,
        provider: 'fcm'
      };
    } else {
      throw new Error(`FCM推送失败: ${response.data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('❌ FCM推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      provider: 'fcm'
    };
  }
};

/**
 * 通过HMS发送推送
 * @param {Array} devices 华为设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendHMSNotification = async (devices, message) => {
  try {
    const { title, content, data } = message;
    
    // 这里应该实现真正的HMS推送逻辑
    // 需要使用华为的推送服务
    
    console.log('📱 HMS推送:', {
      devices: devices.length,
      message: { title, content }
    });
    
    // 模拟推送结果
    return {
      success: true,
      message_id: `hms_${Date.now()}`,
      sent_count: devices.length,
      provider: 'hms'
    };
  } catch (error) {
    console.error('❌ HMS推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      provider: 'hms'
    };
  }
};

/**
 * 统一推送消息到多个设备
 * @param {Array} devices 设备列表
 * @param {Object} message 推送消息
 * @returns {Promise<Object>} 推送结果
 */
const sendPushNotification = async (devices, message) => {
  try {
    if (!devices || devices.length === 0) {
      return {
        success: true,
        message: '没有设备需要推送',
        results: []
      };
    }
    
    // 按推送提供商分组设备
    const providerGroups = {};
    devices.forEach(device => {
      const provider = device.push_provider || 'jpush';
      if (!providerGroups[provider]) {
        providerGroups[provider] = [];
      }
      providerGroups[provider].push(device);
    });
    
    const results = [];
    
    // 并发推送到不同的提供商
    const pushPromises = Object.entries(providerGroups).map(async ([provider, providerDevices]) => {
      let result;
      
      switch (provider) {
        case 'jpush':
          result = await sendJPushNotification(providerDevices, message);
          break;
        case 'apns':
          result = await sendAPNSNotification(providerDevices, message);
          break;
        case 'fcm':
          result = await sendFCMNotification(providerDevices, message);
          break;
        case 'hms':
          result = await sendHMSNotification(providerDevices, message);
          break;
        default:
          result = await sendJPushNotification(providerDevices, message);
      }
      
      results.push({
        provider,
        device_count: providerDevices.length,
        ...result
      });
      
      return result;
    });
    
    await Promise.all(pushPromises);
    
    const totalSent = results.reduce((sum, r) => sum + (r.sent_count || 0), 0);
    const totalFailed = results.reduce((sum, r) => sum + (r.failed_count || 0), 0);
    const allSuccess = results.every(r => r.success);
    
    return {
      success: allSuccess,
      message: `推送完成: 成功${totalSent}个, 失败${totalFailed}个`,
      total_devices: devices.length,
      sent_count: totalSent,
      failed_count: totalFailed,
      results
    };
  } catch (error) {
    console.error('❌ 统一推送失败:', error.message);
    return {
      success: false,
      message: error.message,
      results: []
    };
  }
};

/**
 * 发送消息通知推送（智能预览版本）
 * @param {Array} devices 设备列表
 * @param {Object} messageInfo 消息信息
 * @returns {Promise<Object>} 推送结果
 */
const sendMessageNotification = async (devices, messageInfo) => {
  const { message_type, metadata, content, file_name, file_size, mime_type, file_extension } = messageInfo;

  // 生成智能消息预览
  const messagePreview = generateIntelligentMessagePreview({
    message_type,
    content,
    file_name,
    file_size,
    mime_type,
    file_extension,
    metadata
  });

  return await sendPushNotification(devices, {
    title: messagePreview.title,
    content: messagePreview.content,
    data: {
      type: 'new_message',
      message_type,
      message_id: messageInfo.message_id,
      timestamp: Date.now(),
      preview: messagePreview,
      // 添加文件信息用于移动端处理
      file_info: {
        name: file_name,
        size: file_size,
        type: mime_type,
        extension: file_extension
      }
    }
  });
};

/**
 * 生成智能消息预览
 * @param {Object} messageData 消息数据
 * @returns {Object} 预览信息
 */
const generateIntelligentMessagePreview = (messageData) => {
  const { message_type, content, file_name, file_size, mime_type, file_extension, metadata } = messageData;

  // 获取发送者信息
  const senderName = metadata?.senderName || metadata?.from_user || '联系人';
  const shortSenderName = senderName.length > 8 ? senderName.substring(0, 8) + '...' : senderName;

  switch (message_type) {
    case 'text':
      // 文本消息显示前30个字符
      const textPreview = content && content.length > 30
        ? content.substring(0, 30) + '...'
        : content || '发送了一条消息';
      return {
        title: `${shortSenderName}`,
        content: textPreview,
        icon: '💬',
        category: 'message'
      };

    case 'image':
      return {
        title: `${shortSenderName} 发送了图片`,
        content: file_name ? `图片: ${file_name}` : '查看图片',
        icon: '🖼️',
        category: 'media'
      };

    case 'voice':
      const duration = metadata?.duration || 0;
      const durationText = duration > 0 ? ` (${duration}秒)` : '';
      return {
        title: `${shortSenderName} 发送了语音`,
        content: `语音消息${durationText}`,
        icon: '🎵',
        category: 'media'
      };

    case 'video':
      const videoSize = file_size ? formatFileSize(file_size) : '';
      const videoSizeText = videoSize ? ` (${videoSize})` : '';
      return {
        title: `${shortSenderName} 发送了视频`,
        content: `${file_name || '视频文件'}${videoSizeText}`,
        icon: '🎬',
        category: 'media'
      };

    case 'file':
      // 根据文件类型生成智能预览
      const fileTypeInfo = getFileTypeInfo(mime_type, file_extension, file_name);
      const fileSizeText = file_size ? ` (${formatFileSize(file_size)})` : '';

      return {
        title: `${shortSenderName} 发送了${fileTypeInfo.description}`,
        content: `${file_name || '文件'}${fileSizeText}`,
        icon: fileTypeInfo.icon,
        category: 'file'
      };

    case 'location':
      const locationLabel = metadata?.location_label || '位置信息';
      return {
        title: `${shortSenderName} 发送了位置`,
        content: locationLabel,
        icon: '📍',
        category: 'location'
      };

    case 'link':
      const linkTitle = metadata?.link_title || '链接';
      const linkUrl = metadata?.link_url || '';
      const domain = linkUrl ? extractDomain(linkUrl) : '';
      return {
        title: `${shortSenderName} 分享了链接`,
        content: `${linkTitle}${domain ? ` (${domain})` : ''}`,
        icon: '🔗',
        category: 'link'
      };

    case 'event':
      const eventType = metadata?.event_type || '系统事件';
      return {
        title: '系统通知',
        content: `${eventType}`,
        icon: '⚡',
        category: 'system'
      };

    default:
      return {
        title: `${shortSenderName}`,
        content: '发送了一条消息',
        icon: '📨',
        category: 'unknown'
      };
  }
};

/**
 * 获取文件类型信息
 * @param {string} mimeType MIME类型
 * @param {string} fileExtension 文件扩展名
 * @param {string} fileName 文件名
 * @returns {Object} 文件类型信息
 */
const getFileTypeInfo = (mimeType, fileExtension, fileName) => {
  const extension = fileExtension || (fileName ? fileName.split('.').pop()?.toLowerCase() : '');

  // PDF文件
  if (mimeType?.includes('pdf') || extension === 'pdf') {
    return { description: 'PDF文档', icon: '📄' };
  }

  // OFD文件
  if (mimeType?.includes('ofd') || extension === 'ofd') {
    return { description: 'OFD文档', icon: '📋' };
  }

  // Word文档
  if (mimeType?.includes('word') || ['doc', 'docx'].includes(extension)) {
    return { description: 'Word文档', icon: '📝' };
  }

  // Excel表格
  if (mimeType?.includes('excel') || ['xls', 'xlsx'].includes(extension)) {
    return { description: 'Excel表格', icon: '📊' };
  }

  // PowerPoint演示
  if (mimeType?.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) {
    return { description: 'PPT演示', icon: '📽️' };
  }

  // 文本文件
  if (mimeType?.includes('text') || ['txt', 'rtf'].includes(extension)) {
    return { description: '文本文件', icon: '📃' };
  }

  // 压缩文件
  if (['zip', 'rar', '7z'].includes(extension)) {
    return { description: '压缩文件', icon: '📦' };
  }

  // 图片文件
  if (mimeType?.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return { description: '图片', icon: '🖼️' };
  }

  // 视频文件
  if (mimeType?.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv'].includes(extension)) {
    return { description: '视频', icon: '🎬' };
  }

  // 音频文件
  if (mimeType?.startsWith('audio/') || ['mp3', 'wav', 'aac', 'amr'].includes(extension)) {
    return { description: '音频', icon: '🎵' };
  }

  // 默认文件
  return { description: '文件', icon: '📁' };
};

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0B';

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(i === 0 ? 0 : 1);

  return `${size}${sizes[i]}`;
};

/**
 * 从URL中提取域名
 * @param {string} url URL地址
 * @returns {string} 域名
 */
const extractDomain = (url) => {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return urlObj.hostname;
  } catch (error) {
    return '';
  }
};

/**
 * 发送下载指令推送 (旧版本 - 兼容性保留)
 * @param {Array} devices 设备列表
 * @param {Object} downloadInfo 下载信息
 * @returns {Promise<Object>} 推送结果
 */
const sendDownloadNotification = async (devices, downloadInfo) => {
  const { media_id, media_type, filename, access_token } = downloadInfo;
  
  return await sendPushNotification(devices, {
    title: '文件下载',
    content: `可以下载文件: ${filename || '未知文件'}`,
    data: {
      type: 'download_instruction',
      media_id,
      media_type,
      filename,
      access_token,
      timestamp: Date.now()
    }
  });
};

/**
 * 发送新的文件下载通知推送 (支持下载链接)
 * @param {Array} devices 设备列表
 * @param {Object} fileInfo 文件信息
 * @returns {Promise<Object>} 推送结果
 */
const sendFileDownloadNotification = async (devices, fileInfo) => {
  const { 
    messageId, 
    fileName, 
    fileSize, 
    contentType, 
    downloadUrl, 
    downloadExpiresAt,
    messageType = 'file'
  } = fileInfo;
  
  // 根据文件类型生成不同的通知内容
  let title, content;
  switch (messageType) {
    case 'image':
      title = '📸 收到图片';
      content = `图片文件: ${fileName || '未知图片'}`;
      break;
    case 'voice':
      title = '🎵 收到语音';
      content = `语音文件: ${fileName || '语音消息'}`;
      break;
    case 'video':
      title = '🎬 收到视频';
      content = `视频文件: ${fileName || '未知视频'}`;
      break;
    case 'file':
      title = '📁 收到文件';
      content = `文件: ${fileName || '未知文件'}`;
      break;
    default:
      title = '📎 收到附件';
      content = `附件: ${fileName || '未知文件'}`;
  }
  
  // 添加文件大小信息
  if (fileSize) {
    const sizeInMB = (fileSize / 1024 / 1024).toFixed(2);
    content += ` (${sizeInMB}MB)`;
  }
  
  return await sendPushNotification(devices, {
    title,
    content,
    data: {
      type: 'file_download_ready',
      message_id: messageId,
      message_type: messageType,
      file_name: fileName,
      file_size: fileSize,
      content_type: contentType,
      download_url: downloadUrl,
      download_expires_at: downloadExpiresAt,
      timestamp: Date.now()
    }
  });
};

/**
 * 检查推送服务配置
 * @returns {Object} 配置检查结果
 */
const checkPushConfig = () => {
  const results = {};
  
  // 检查JPush配置
  results.jpush = {
    configured: !!(JPUSH_CONFIG.app_key && JPUSH_CONFIG.master_secret),
    app_key: JPUSH_CONFIG.app_key ? '已配置' : '未配置',
    master_secret: JPUSH_CONFIG.master_secret ? '已配置' : '未配置'
  };
  
  // 检查FCM配置
  results.fcm = {
    configured: !!process.env.FCM_SERVER_KEY,
    server_key: process.env.FCM_SERVER_KEY ? '已配置' : '未配置'
  };
  
  // 检查APNS配置
  results.apns = {
    configured: !!(process.env.APNS_KEY_ID && process.env.APNS_TEAM_ID),
    key_id: process.env.APNS_KEY_ID ? '已配置' : '未配置',
    team_id: process.env.APNS_TEAM_ID ? '已配置' : '未配置'
  };
  
  // 检查HMS配置
  results.hms = {
    configured: !!(process.env.HMS_CLIENT_ID && process.env.HMS_CLIENT_SECRET),
    client_id: process.env.HMS_CLIENT_ID ? '已配置' : '未配置',
    client_secret: process.env.HMS_CLIENT_SECRET ? '已配置' : '未配置'
  };
  
  return results;
};

/**
 * 获取支持的推送提供商列表
 * @returns {Array} 推送提供商列表
 */
const getSupportedPushProviders = () => {
  return Object.keys(PUSH_PROVIDERS).map(provider => ({
    provider,
    name: PUSH_PROVIDERS[provider].name,
    configured: checkPushConfig()[provider]?.configured || false
  }));
};

/**
 * 发送微信绑定成功通知
 * @param {Array} devices 设备列表
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object>} 推送结果
 */
const sendBindingSuccessNotification = async (devices, userUuid) => {
  return await sendPushNotification(devices, {
    title: '微信绑定成功',
    content: '您的微信账号已成功绑定到公职猫，现在可以转发消息了！',
    data: {
      type: 'wechat_binding_success',
      user_uuid: userUuid,
      binding_time: Date.now(),
      timestamp: Date.now()
    }
  });
};

module.exports = {
  sendFileDownloadNotification,
  sendBindingSuccessNotification,
  checkPushConfig,
  getSupportedPushProviders,
  PUSH_PROVIDERS
}; 