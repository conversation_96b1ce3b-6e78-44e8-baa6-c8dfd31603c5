# 微信转发功能架构修复方案

## 问题分析

当前架构存在严重问题：移动端尝试直接调用企业微信API，但这是不可行的，因为：

1. **可信IP限制**：企业微信API只允许可信IP调用，移动端IP无法加入白名单
2. **安全性问题**：access_token和corp_secret不应暴露给移动端
3. **架构不合理**：违反了企业微信的安全设计原则

## 当前错误架构

```
移动端 → 获取access_token → 直接调用企业微信API ❌
```

## 正确架构

```
企业微信 → Webhook → 轻量服务器(可信IP) → 处理消息 → 存储到数据库 → 下载并缓存文件
                                                        ↓
移动端 ← 我们的API ← 轻量服务器 ← 查询数据库 ← 提供文件下载服务
```

## 修复方案

### 1. 轻量服务器端修复

#### 1.1 完善消息接收和处理
- ✅ 接收企业微信webhook推送
- ✅ 调用企业微信读取消息API
- ✅ 处理绑定令牌
- ✅ 存储消息到数据库
- ✅ 代理下载媒体文件并加密缓存

#### 1.2 提供移动端API
- `GET /api/sync/messages` - 获取用户消息列表和下载链接
- `POST /api/sync/ack` - 确认消息已读
- `GET /api/bind/status` - 查询绑定状态
- `GET /api/media/download/:token` - 安全下载文件

### 2. 移动端修复

#### 2.1 移除企业微信API调用
需要移除以下功能：
- `WeChatAPIService.getAccessToken()`
- `WeChatAPIService.syncMessages()`
- 所有直接调用企业微信API的代码

#### 2.2 改为调用我们的API
- 消息同步改为调用 `/api/sync/messages`
- 绑定状态查询改为调用 `/api/bind/status`
- 媒体文件下载改为调用我们的 `/api/media/download/:token`

### 3. 数据流向

#### 3.1 消息接收流程
```
1. 用户在微信发送消息
2. 企业微信推送webhook到轻量服务器
3. 轻量服务器调用企业微信API读取消息详情
4. 轻量服务器解析消息内容（包括绑定令牌）
5. 轻量服务器存储消息元数据到数据库
6. 如果是媒体文件，轻量服务器下载并加密缓存文件
7. 轻量服务器发送推送通知给移动端
```

#### 3.2 消息同步流程
```
1. 移动端收到推送通知
2. 移动端调用我们的API获取新消息
3. 轻量服务器从数据库查询用户消息
4. 返回消息列表和文件下载链接给移动端
5. 移动端显示消息并根据需要下载文件
```

#### 3.3 绑定流程
```
1. 移动端生成绑定令牌
2. 用户在微信客服发送绑定令牌
3. 企业微信推送消息到轻量服务器
4. 轻量服务器识别绑定令牌并创建绑定关系
5. 移动端轮询绑定状态或收到推送通知
```

#### 3.4 文件下载流程
```
1. 移动端从消息列表获取文件下载链接
2. 移动端调用 /api/media/download/:token
3. 轻量服务器验证令牌并解密文件
4. 流式传输文件给移动端
5. 文件下载完成后，服务器删除缓存文件
```

## 实施步骤

### 阶段1：轻量服务器完善（当前进行中）
- [x] 修复webhook消息接收
- [x] 实现企业微信消息读取API调用
- [x] 完善绑定令牌处理
- [x] 完善消息存储到数据库
- [x] 实现移动端API接口
- [ ] 实现文件代理下载和加密缓存
- [ ] 实现文件清理机制

### 阶段2：移动端重构
- [ ] 移除企业微信API调用代码
- [ ] 重构消息同步服务
- [ ] 修改为调用我们的API
- [ ] 实现新的文件下载逻辑
- [ ] 测试完整流程

### 阶段3：测试验证
- [ ] 端到端测试绑定流程
- [ ] 测试消息同步功能
- [ ] 验证文件下载和缓存清理
- [ ] 验证推送通知
- [ ] 性能和稳定性测试

## 关键修改点

### 移动端需要修改的文件
1. `WeChatAPIService.ts` - 移除企业微信API调用
2. `WeChatMessageSyncService.ts` - 改为调用我们的API
3. `SimpleWeChatBindingService.ts` - 修改绑定状态检查逻辑

### 轻量服务器需要完善的功能
1. 消息存储到数据库
2. 用户消息查询API
3. 消息状态管理
4. 推送通知集成
5. 文件代理下载服务
6. 文件加密缓存机制
7. 自动清理过期文件

## 预期效果

修复后的架构将实现：
- ✅ 符合企业微信安全要求
- ✅ 移动端无需企业微信API权限
- ✅ 更好的安全性和稳定性
- ✅ 支持离线消息查看和文件下载
- ✅ 统一的消息管理
- ✅ 文件隐私保护（阅后即焚）
- ✅ 无云函数限制，支持大文件处理
