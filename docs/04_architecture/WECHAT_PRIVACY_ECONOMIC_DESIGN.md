# 微信转发功能隐私保护与经济性设计方案

## 设计原则

1. **临时加密缓存**：下载文件并加密存储，用户下载后立即删除
2. **阅后即焚**：文件下载后或3天后自动删除，确保零长期存储
3. **服务器代理**：由服务器处理大文件下载，解决云函数限制
4. **隐私优先**：所有文件加密存储，最小化数据暴露时间

## 核心架构设计

### 1. 数据库表结构（优化后）

```sql
-- 用户绑定表（已存在）
CREATE TABLE wechat_user_bindings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_uuid VARCHAR(36) NOT NULL,
    external_userid VARCHAR(64) NOT NULL,
    binding_status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_binding (user_uuid),
    UNIQUE KEY unique_external_user (external_userid)
);

-- 消息记录表（包含文件缓存信息）
CREATE TABLE message_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_uuid VARCHAR(36) NOT NULL,
    wechat_message_id VARCHAR(64) NOT NULL,
    message_type ENUM('text', 'image', 'voice', 'video', 'file', 'location', 'link') NOT NULL,
    message_time TIMESTAMP NOT NULL,
    content TEXT NULL,  -- 文本消息内容
    file_path VARCHAR(512) NULL,  -- 加密文件存储路径
    file_name VARCHAR(255) NULL,  -- 原始文件名
    file_size BIGINT NULL,  -- 文件大小
    download_token VARCHAR(64) NULL,  -- 一次性下载令牌
    download_expires_at TIMESTAMP NULL,  -- 下载链接过期时间
    file_expires_at TIMESTAMP NULL,  -- 文件缓存过期时间（3天）
    downloaded BOOLEAN DEFAULT FALSE,  -- 是否已下载
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_time (user_uuid, message_time),
    INDEX idx_file_expires (file_expires_at),
    INDEX idx_download_token (download_token),
    INDEX idx_downloaded (downloaded)
);

-- 定时清理任务
CREATE EVENT IF NOT EXISTS cleanup_expired_files
ON SCHEDULE EVERY 1 HOUR
DO
BEGIN
    -- 删除已下载或过期的文件记录
    DELETE FROM message_records 
    WHERE (downloaded = TRUE AND created_at < NOW() - INTERVAL 1 HOUR)
       OR (file_expires_at IS NOT NULL AND file_expires_at < NOW());
END;
```

### 2. 消息处理流程（临时缓存）

#### 2.1 文本消息处理
```javascript
// 存储文本消息内容
const processTextMessage = async (message) => {
    const record = {
        user_uuid: getUserUuidByExternalId(message.external_userid),
        wechat_message_id: message.msgid,
        message_type: 'text',
        message_time: new Date(message.send_time * 1000),
        content: message.content,  // 直接存储文本内容
        download_token: null,  // 文本消息无需下载令牌
        created_at: new Date()
    };
    
    // 存储消息记录到数据库
    await insertMessageRecord(record);
    
    // 立即推送通知给用户
    await sendPushNotification(record.user_uuid, {
        type: 'new_message',
        message_type: 'text',
        message_id: record.id
    });
};
```

#### 2.2 媒体文件处理（服务器下载缓存）
```javascript
// 下载文件并加密缓存
const processMediaMessage = async (message) => {
    try {
        // 1. 从企业微信下载文件
        const accessToken = await getWeChatAccessToken();
        const fileData = await downloadFromWeChat(accessToken, message.media_id);
        
        // 2. 生成加密存储路径和令牌
        const fileId = generateUUID();
        const encryptedPath = `cache/encrypted/${fileId}`;
        const downloadToken = generateOneTimeToken();
        
        // 3. 加密并存储文件
        await encryptAndSaveFile(fileData, encryptedPath);
        
        // 4. 创建消息记录
        const record = {
            user_uuid: getUserUuidByExternalId(message.external_userid),
            wechat_message_id: message.msgid,
            message_type: message.msgtype,
            message_time: new Date(message.send_time * 1000),
            file_path: encryptedPath,
            file_name: message.filename || `${message.msgtype}_${Date.now()}`,
            file_size: fileData.length,
            download_token: downloadToken,
            download_expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
            file_expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天
            downloaded: false
        };
        
        await insertMessageRecord(record);
        
        // 5. 推送通知给用户
        await sendPushNotification(record.user_uuid, {
            type: 'new_message',
            message_type: message.msgtype,
            message_id: record.id,
            download_url: `/api/media/download/${downloadToken}`,
            file_name: record.file_name,
            file_size: record.file_size
        });
        
    } catch (error) {
        console.error('媒体文件处理失败:', error);
        // 记录错误，但不阻塞流程
    }
};
```

### 3. 移动端API设计

#### 3.1 获取消息列表
```javascript
// GET /api/sync/messages
{
    "success": true,
    "data": {
        "messages": [
            {
                "id": 123,
                "message_type": "text",
                "message_time": "2025-01-19T10:30:00Z",
                "content": "用户发送的文本消息",
                "created_at": "2025-01-19T10:30:00Z"
            },
            {
                "id": 124,
                "message_type": "image",
                "message_time": "2025-01-19T10:31:00Z",
                "file_name": "image.jpg",
                "file_size": 1024000,
                "download_url": "/api/media/download/eyJ0eXAiOiJKV1Q...",
                "download_expires_at": "2025-01-20T10:31:00Z",
                "created_at": "2025-01-19T10:31:00Z"
            }
        ],
        "has_more": false,
        "next_since_id": 125
    }
}
```

#### 3.2 安全文件下载
```javascript
// GET /api/media/download/:token
// 响应：直接返回文件流，下载后立即删除缓存文件
```

### 4. 实现细节

#### 4.1 文件加密存储
```javascript
const encryptAndSaveFile = async (fileData, filePath) => {
    const key = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', key);
    
    let encrypted = cipher.update(fileData);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    // 存储加密文件和密钥
    await fs.writeFile(filePath, encrypted);
    await fs.writeFile(`${filePath}.key`, key);
    await fs.writeFile(`${filePath}.iv`, iv);
};

const decryptAndReadFile = async (filePath) => {
    const key = await fs.readFile(`${filePath}.key`);
    const iv = await fs.readFile(`${filePath}.iv`);
    const encrypted = await fs.readFile(filePath);
    
    const decipher = crypto.createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encrypted);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
};
```

#### 4.2 阅后即焚下载
```javascript
const handleFileDownload = async (req, res) => {
    const { token } = req.params;
    
    try {
        // 验证令牌并获取文件信息
        const record = await validateDownloadToken(token);
        
        if (!record || record.downloaded || record.download_expires_at < new Date()) {
            return res.status(404).json({ error: '文件不存在或已过期' });
        }
        
        // 解密并流式传输文件
        const fileData = await decryptAndReadFile(record.file_path);
        
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${record.file_name}"`);
        res.setHeader('Content-Length', fileData.length);
        
        res.send(fileData);
        
        // 下载完成后立即删除文件和记录
        await deleteFileAndRecord(record);
        
    } catch (error) {
        console.error('文件下载失败:', error);
        res.status(500).json({ error: '下载失败' });
    }
};

const deleteFileAndRecord = async (record) => {
    try {
        // 删除加密文件和密钥
        await fs.unlink(record.file_path);
        await fs.unlink(`${record.file_path}.key`);
        await fs.unlink(`${record.file_path}.iv`);
        
        // 标记为已下载（用于清理任务）
        await markAsDownloaded(record.id);
    } catch (error) {
        console.error('文件清理失败:', error);
    }
};
```

#### 4.3 自动清理机制
```javascript
// 定时清理任务（每小时执行）
const cleanupExpiredFiles = async () => {
    const expiredRecords = await db.query(`
        SELECT file_path FROM message_records 
        WHERE (downloaded = TRUE AND created_at < NOW() - INTERVAL 1 HOUR)
           OR (file_expires_at IS NOT NULL AND file_expires_at < NOW())
    `);
    
    // 删除过期的文件
    for (const record of expiredRecords) {
        if (record.file_path) {
            try {
                await fs.unlink(record.file_path);
                await fs.unlink(`${record.file_path}.key`);
                await fs.unlink(`${record.file_path}.iv`);
            } catch (error) {
                console.error('文件删除失败:', error);
            }
        }
    }
    
    // 删除过期的数据库记录
    const result = await db.execute(`
        DELETE FROM message_records 
        WHERE (downloaded = TRUE AND created_at < NOW() - INTERVAL 1 HOUR)
           OR (file_expires_at IS NOT NULL AND file_expires_at < NOW())
    `);
    
    console.log(`清理了 ${result.affectedRows} 条过期记录和文件`);
};
```

## 优势分析

### 1. 隐私保护
- ✅ 文件在服务器端加密存储，无法直接访问
- ✅ 阅后即焚机制，下载后立即删除
- ✅ 3天自动过期，确保零长期存储
- ✅ 一次性下载令牌，防止重复访问

### 2. 经济性
- ✅ 临时缓存策略，无长期存储成本
- ✅ 自动清理机制，减少存储占用
- ✅ 轻量服务器按需付费，成本可控
- ✅ 无云函数执行时长限制

### 3. 技术优势
- ✅ 支持大文件处理，无云函数限制
- ✅ 服务器代理下载，客户端逻辑简化
- ✅ 加密存储，安全性更高
- ✅ 流式传输，内存占用低

### 4. 合规性
- ✅ 数据最小化原则
- ✅ 临时存储，非持久化
- ✅ 自动过期清理
- ✅ 审计友好

## 实施计划

### 阶段1：数据库和存储设计
- [ ] 创建message_records表
- [ ] 设置文件存储目录结构
- [ ] 实现加密存储机制
- [ ] 设置自动清理任务

### 阶段2：轻量服务器API
- [ ] 实现文件下载和加密缓存
- [ ] 实现安全文件下载接口
- [ ] 实现消息同步API
- [ ] 实现自动清理机制

### 阶段3：移动端适配
- [ ] 修改消息同步逻辑
- [ ] 实现文件下载功能
- [ ] 测试完整流程

这个方案在保障用户隐私的同时，通过临时加密缓存解决了大文件处理问题，实现了功能性和隐私保护的完美平衡。
