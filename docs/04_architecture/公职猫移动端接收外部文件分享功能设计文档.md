
# 公职猫移动端接收外部文件分享功能设计文档

- **最后更新**: {{CURRENT_DATE}}
- **负责人**: AI助手 (芝审官)
- **状态**: 设计中

## 1. 需求背景与用户场景 (现象分析)

### 1.1. 用户场景

政务人员在日常工作中，经常需要在不同的应用间流转文件。例如：

- 从“微信”、“钉钉”等即时通讯工具中，接收同事发来的工作文档（.docx, .pdf, .xlsx）。
- 从邮件客户端中，保存重要的通知公告附件。
- 从浏览器中，将下载的政策文件或参考资料直接存入公职猫进行归档和后续处理。

当前，用户需要先将文件保存到本地，再手动打开公-  职猫App进行导入，操作路径繁琐，效率低下。

### 1.2. 核心问题

应用缺乏一个直接、高效的通道来接收来自其他App分享的数据，这与公职猫作为“秘书般懂你”的智能助手的定位不符，影响了产品的流畅体验和数据聚合能力。

### 1.3. 功能目标

实现一个标准的系统级分享接收功能，允许用户在任何支持分享的应用中，直接将文件、文本或链接“分享”到公职猫App，并由App进行后续的解析和处理。

## 2. 技术方案与架构设计 (逻辑链回溯)

### 2.1. 核心技术选型

经过对现有依赖和社区成熟方案的调研，我们确定核心技术方案如下：

- **核心库**: `react-native-receive-sharing-intent`
  - **选型理由**:
    - **功能完备**: 专门为接收分享意图而设计，支持文件、文本、URL等多种类型。
    - **社区成熟**: 社区活跃，有大量的实践案例和问题解决方案。
    - **跨平台一致性**: 提供了一套在JS层处理iOS和Android平台差异的API，简化了开发。
    - **符合“不重复造轮子”原则**: 避免了我们手动编写大量复杂的原生代码。

### 2.2. 平台特定实现方案

#### 2.2.1. Android平台

1.  **机制**: 利用Android系统的`Intent`和`Intent Filter`机制。
2.  **配置文件**: 在 `android/app/src/main/AndroidManifest.xml` 中，为 `MainActivity` 添加 `<intent-filter>`。
3.  **声明支持的操作**:
    - `android.intent.action.SEND`: 处理单个分享项目。
    - `android.intent.action.SEND_MULTIPLE`: 处理多个分享项目。
4.  **声明支持的数据类型 (MIME Type)**:
    - `text/*`: 接收文本和链接。
    - `image/*`: 接收图片文件。
    - `video/*`: 接收视频文件。
    - `application/pdf`: 接收PDF文件。
    - `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`: 接收Word文档。
    - `*/*`: (备选) 接收任意类型的文件，以提供最大的兼容性。
5.  **数据流**: 第三方App发起分享 -> Android系统根据Intent Filter将数据路由到公职猫App -> `react-native-receive-sharing-intent` 的原生模块监听到Intent -> 将数据（文件路径、文本内容等）通过Bridge传递给JS层。

#### 2.2.2. iOS平台

1.  **机制**: 利用iOS的 **Share Extension** (分享扩展) 和 **App Groups** (应用组)。
2.  **Share Extension**:
    - 需要在Xcode中为项目创建一个新的Target，类型为 "Share Extension"。
    - 这个扩展是独立于主应用的微型应用，它会在系统的分享面板中显示公职猫的图标。
3.  **App Groups**:
    - 用于在主应用和分享扩展之间安全地共享数据。
    - 分享扩展接收到文件后，会将其保存到App Group的共享容器中。
4.  **数据流**: 第三方App发起分享 -> 用户在分享面板选择公职猫 -> Share Extension被激活并接收文件 -> 扩展将文件拷贝到App Group共享目录 -> 主应用通过 `react-native-receive-sharing-intent` 提供的API从共享目录中读取文件信息。
5.  **URL Scheme**:
    - 需要定义一个自定义的URL Scheme（如 `gongzhimall://`），用于从分享扩展唤醒并向主应用传递数据。

### 2.3. JavaScript层逻辑

1.  **统一监听**: 在App的根组件（如 `App.tsx`）中，使用 `ReceiveSharingIntent.getReceivedFiles` 方法来监听分享事件。
2.  **事件处理**:
    - App启动时，检查是否有待处理的分享数据（冷启动场景）。
    - App在后台运行时，监听新的分享事件（热启动场景）。
3.  **数据解析**:
    - 从回调函数中获取分享的文件数组，每个文件对象包含`filePath`, `text`, `weblink`, `mimeType`, `contentUri`, `fileName`等信息。
4.  **路由与响应**:
    - 根据`mimeType`判断文件类型。
    - 弹出一个统一的“文件接收”处理模态框或页面。
    - 模态框中应显示文件名、大小、来源App（如果可获取）等信息。
    - 提供操作选项，如“保存到收件箱”、“创建新笔记”、“取消”等。
    - 将文件从临时目录（共享目录）拷贝到应用内部的安全存储位置。

## 3. 风险与B计划 (最后一环人格)

- **风险点1**: **权限问题**。在Android 10+和iOS上，对文件系统的访问权限日益收紧。
  - **应对**: 严格遵循库的指引进行权限申请，特别是Android的`READ_EXTERNAL_STORAGE`权限和分区存储的适配。确保在需要时才向用户请求权限。
- **风险点2**: **大文件处理**。分享非常大的文件（如高清视频）可能导致应用卡顿或崩溃。
  - **应对**: 在接收到文件信息后，首先检查文件大小。对超过预设阈值（如100MB）的文件进行提示，并考虑在后台进行文件的复制和处理，避免阻塞UI线程。
- **风险点3**: **平台兼容性**。不同Android厂商的ROM和iOS版本可能存在细微差异。
  - **应对**: 在主流设备和系统版本上进行充分测试（华为、小米、iOS 16+等）。
- **B计划**: 如果`react-native-receive-sharing-intent`在某些极端场景下出现无法解决的bug，备选方案是 `react-native-share-menu`，但前者目前是社区首选。最坏情况下，需要投入原生开发资源进行定制化开发。

## 4. 验收标准 (任务完成自查)

- ✅ **功能层面**:
  - 可以在微信中成功分享文本、链接、图片、PDF和Word文档到公职猫App。
  - 可以在系统文件管理器中成功分享文件到公职猫App。
  - App能正确识别分享的内容类型，并弹出对应的处理界面。
  - 文件被成功保存到App的内部存储中。
- ✅ **体验层面**:
  - 分享过程流畅，无明显卡顿。
  - 接收界面清晰友好，操作指引明确。
  - 对不支持的文件类型或分享失败有明确的错误提示。
- ✅ **代码层面**:
  - 原生配置（AndroidManifest.xml, Info.plist等）正确无误。
  - JS层的事件处理逻辑健壮，覆盖冷热启动场景。
  - 代码遵循项目现有规范。 