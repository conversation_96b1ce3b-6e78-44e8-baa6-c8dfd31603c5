#!/usr/bin/env node

/**
 * 环境检测脚本 - 检查开发环境配置
 */

const chalk = require('chalk');

function checkEnvironment() {
  console.log(chalk.blue('🔍 检查开发环境配置...\n'));

  // 检查NODE_ENV
  const nodeEnv = process.env.NODE_ENV;
  console.log(`NODE_ENV: ${nodeEnv || chalk.red('未设置')}`);
  
  if (!nodeEnv) {
    console.log(chalk.yellow('⚠️  警告: NODE_ENV未设置，建议设置为development'));
  } else if (nodeEnv === 'development') {
    console.log(chalk.green('✅ NODE_ENV正确设置为development'));
  } else if (nodeEnv === 'production') {
    console.log(chalk.red('❌ NODE_ENV设置为production，开发时应该使用development'));
  }

  // 检查React Native版本
  try {
    const packageJson = require('../package.json');
    const rnVersion = packageJson.dependencies['react-native'];
    console.log(`React Native版本: ${rnVersion}`);
  } catch (error) {
    console.log(chalk.red('❌ 无法读取package.json'));
  }

  // 提供建议
  console.log('\n' + chalk.blue('💡 建议的启动命令:'));
  console.log(chalk.green('  开发模式: NODE_ENV=development yarn start --reset-cache'));
  console.log(chalk.green('  或使用:   yarn start:dev'));
  
  console.log('\n' + chalk.blue('🧹 如果遇到问题，尝试清理:'));
  console.log('  rm -rf node_modules && yarn install');
  console.log('  yarn start --reset-cache');
}

if (require.main === module) {
  checkEnvironment();
}

module.exports = { checkEnvironment };
