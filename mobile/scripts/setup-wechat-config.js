#!/usr/bin/env node

/**
 * 微信配置设置脚本
 * 提供交互式的配置设置功能
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 颜色输出工具
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 创建输入接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 提示用户输入
function askQuestion(question, defaultValue = '') {
  return new Promise((resolve) => {
    const prompt = defaultValue
      ? `${question} (默认: ${defaultValue}): `
      : `${question}: `;

    rl.question(prompt, (answer) => {
      resolve(answer.trim() || defaultValue);
    });
  });
}

// 配置项定义
const configItems = [
  {
    key: 'JPUSH_APP_KEY',
    name: '极光推送AppKey',
    description: '从极光推送控制台获取 (https://www.jiguang.cn/)',
    required: true,
    validator: (value) => {
      if (!value) {return '极光推送AppKey不能为空';}
      if (value.length !== 24) {return '极光推送AppKey长度应为24位';}
      return null;
    },
  },
  {
    key: 'JPUSH_CHANNEL',
    name: '极光推送渠道',
    description: '推送渠道标识',
    required: false,
    defaultValue: 'gongzhimall-official',
  },
  {
    key: 'WECHAT_CORP_ID',
    name: '企业微信CorpID',
    description: '从企业微信管理后台获取 (https://work.weixin.qq.com/)',
    required: true,
    validator: (value) => {
      if (!value) {return '企业微信CorpID不能为空';}
      if (!value.startsWith('ww')) {return '企业微信CorpID应以ww开头';}
      return null;
    },
  },
  {
    key: 'WECHAT_AGENT_ID',
    name: '企业微信AgentID',
    description: '企业微信应用的AgentID',
    required: true,
    validator: (value) => {
      if (!value) {return '企业微信AgentID不能为空';}
      if (!/^\d+$/.test(value)) {return '企业微信AgentID应为数字';}
      return null;
    },
  },
  {
    key: 'WECHAT_CORP_SECRET',
    name: '企业微信应用Secret',
    description: '企业微信应用的Secret',
    required: true,
    validator: (value) => {
      if (!value) {return '企业微信应用Secret不能为空';}
      if (value.length < 32) {return '企业微信应用Secret长度不正确';}
      return null;
    },
  },
  {
    key: 'WECHAT_APP_ID',
    name: '微信开放平台AppID',
    description: '从微信开放平台获取 (https://open.weixin.qq.com/)',
    required: true,
    validator: (value) => {
      if (!value) {return '微信开放平台AppID不能为空';}
      if (!value.startsWith('wx')) {return '微信开放平台AppID应以wx开头';}
      return null;
    },
  },
  {
    key: 'WECHAT_APP_SECRET',
    name: '微信开放平台AppSecret',
    description: '微信开放平台应用的Secret',
    required: true,
    validator: (value) => {
      if (!value) {return '微信开放平台AppSecret不能为空';}
      if (value.length < 32) {return '微信开放平台AppSecret长度不正确';}
      return null;
    },
  },
  {
    key: 'WECHAT_BINDING_API_BASE_URL',
    name: '微信转发API地址',
    description: '微信转发服务的API基础地址',
    required: false,
    defaultValue: 'https://wechat.api.gongzhimall.com',
  },
  {
    key: 'WECHAT_BINDING_TOKEN_SECRET',
    name: 'API Token密钥',
    description: '用于API签名验证的密钥',
    required: false,
    defaultValue: 'gongzhimall-app-2025',
  },
  {
    key: 'WECHAT_CUSTOMER_SERVICE_URL',
    name: '企业微信客服链接',
    description: '企业微信客服的访问链接',
    required: false,
    defaultValue: 'https://work.weixin.qq.com/kfid/kfce062e9c6b4dc4f49',
  },
];

// 读取现有配置
function loadExistingConfig() {
  const envPath = path.join(__dirname, '../.env');

  if (!fs.existsSync(envPath)) {
    return {};
  }

  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });

    return envVars;
  } catch (error) {
    log(`读取现有配置失败: ${error.message}`, 'red');
    return {};
  }
}

// 保存配置到.env文件
function saveConfig(config) {
  const envPath = path.join(__dirname, '../.env');

  let envContent = `# 公职猫移动端环境变量配置
# 由 setup-wechat-config.js 脚本生成
# 生成时间: ${new Date().toISOString()}

# ========================================
# 极光推送配置
# ========================================
JPUSH_APP_KEY=${config.JPUSH_APP_KEY || ''}
JPUSH_CHANNEL=${config.JPUSH_CHANNEL || 'gongzhimall-official'}

# ========================================
# 微信相关配置
# ========================================
WECHAT_CORP_ID=${config.WECHAT_CORP_ID || ''}
WECHAT_AGENT_ID=${config.WECHAT_AGENT_ID || ''}
WECHAT_CORP_SECRET=${config.WECHAT_CORP_SECRET || ''}
WECHAT_APP_ID=${config.WECHAT_APP_ID || ''}
WECHAT_APP_SECRET=${config.WECHAT_APP_SECRET || ''}

# ========================================
# API服务配置
# ========================================
WECHAT_BINDING_API_BASE_URL=${config.WECHAT_BINDING_API_BASE_URL || 'https://wechat.api.gongzhimall.com'}
WECHAT_BINDING_TOKEN_SECRET=${config.WECHAT_BINDING_TOKEN_SECRET || 'gongzhimall-app-2025'}
WECHAT_BINDING_MOBILE_APP_TOKEN=${config.WECHAT_BINDING_MOBILE_APP_TOKEN || 'mobile-app-token'}

# ========================================
# 其他配置
# ========================================
WECHAT_CUSTOMER_SERVICE_URL=${config.WECHAT_CUSTOMER_SERVICE_URL || 'https://work.weixin.qq.com/kfid/kfce062e9c6b4dc4f49'}
WECHAT_SYNC_BATCH_SIZE=${config.WECHAT_SYNC_BATCH_SIZE || '50'}
WECHAT_SYNC_MAX_CONCURRENT_MEDIA=${config.WECHAT_SYNC_MAX_CONCURRENT_MEDIA || '3'}
WECHAT_SYNC_TIMEOUT=${config.WECHAT_SYNC_TIMEOUT || '60000'}
`;

  try {
    fs.writeFileSync(envPath, envContent);
    log(`✅ 配置已保存到 ${envPath}`, 'green');
    return true;
  } catch (error) {
    log(`❌ 保存配置失败: ${error.message}`, 'red');
    return false;
  }
}

// 主配置流程
async function setupConfiguration() {
  log('🚀 公职猫微信配置设置工具', 'green');
  log('==================================', 'green');
  log('此工具将帮助您设置微信转发功能所需的配置参数\n', 'cyan');

  const existingConfig = loadExistingConfig();
  const newConfig = {};

  // 显示现有配置
  if (Object.keys(existingConfig).length > 0) {
    log('📋 检测到现有配置:', 'blue');
    configItems.forEach(item => {
      const value = existingConfig[item.key];
      if (value) {
        log(`  ${item.name}: ${value}`, 'cyan');
      }
    });
    log('');
  }

  // 询问是否要修改现有配置
  if (Object.keys(existingConfig).length > 0) {
    const shouldUpdate = await askQuestion('是否要更新现有配置？(y/n)', 'n');
    if (shouldUpdate.toLowerCase() !== 'y') {
      log('配置设置已取消', 'yellow');
      rl.close();
      return;
    }
    log('');
  }

  // 逐一设置配置项
  for (const item of configItems) {
    log(`📝 设置 ${item.name}`, 'blue');
    log(`   ${item.description}`, 'cyan');

    const currentValue = existingConfig[item.key];
    const defaultValue = currentValue || item.defaultValue || '';

    let value;
    let isValid = false;

    while (!isValid) {
      value = await askQuestion(`请输入${item.name}`, defaultValue);

      // 验证输入
      if (item.validator) {
        const error = item.validator(value);
        if (error) {
          log(`❌ ${error}`, 'red');
          continue;
        }
      }

      // 必填项检查
      if (item.required && !value) {
        log(`❌ ${item.name}是必填项`, 'red');
        continue;
      }

      isValid = true;
    }

    newConfig[item.key] = value;
    log(`✅ ${item.name}设置完成\n`, 'green');
  }

  // 确认配置
  log('📊 配置摘要:', 'blue');
  configItems.forEach(item => {
    const value = newConfig[item.key];
    if (value) {
      // 敏感信息脱敏显示
      const displayValue = item.key.includes('SECRET') || item.key.includes('KEY')
        ? value.replace(/./g, '*').slice(0, 8) + '...'
        : value;
      log(`  ${item.name}: ${displayValue}`, 'cyan');
    }
  });

  const shouldSave = await askQuestion('\n确认保存配置？(y/n)', 'y');

  if (shouldSave.toLowerCase() === 'y') {
    const saved = saveConfig(newConfig);
    if (saved) {
      log('\n🎉 配置设置完成！', 'green');
      log('💡 建议运行 yarn wechat:validate 验证配置', 'yellow');
    }
  } else {
    log('配置设置已取消', 'yellow');
  }

  rl.close();
}

// 运行脚本
if (require.main === module) {
  setupConfiguration().catch(error => {
    log(`❌ 配置设置失败: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  setupConfiguration,
  loadExistingConfig,
  saveConfig,
};
