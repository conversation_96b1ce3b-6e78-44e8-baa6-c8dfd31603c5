#!/usr/bin/env node

/**
 * 生产环境配置同步脚本
 * 将后端真实的生产环境配置同步到移动端
 */

const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

function logHeader(message) {
  log(`\n🔍 ${message}`, 'cyan');
  log('='.repeat(60), 'cyan');
}

// 读取后端配置
function readBackendConfig() {
  const backendEnvTemplate = path.join(__dirname, '../../backend/wechat/config/env.template');

  if (!fs.existsSync(backendEnvTemplate)) {
    throw new Error('后端配置文件不存在: ' + backendEnvTemplate);
  }

  const config = {};
  const content = fs.readFileSync(backendEnvTemplate, 'utf8');

  content.split('\n').forEach(line => {
    if (line.trim() && !line.startsWith('#') && line.includes('=')) {
      const [key, value] = line.split('=');
      if (key && value) {
        config[key.trim()] = value.trim();
      }
    }
  });

  return config;
}

// 生成移动端配置
function generateMobileConfig(backendConfig) {
  const mobileConfig = {
    // 微信转发API服务地址（腾讯云函数自定义域名）
    WECHAT_API_BASE_URL: 'https://wechat.api.gongzhimall.com',

    // API访问令牌和签名密钥
    WECHAT_API_TOKEN: backendConfig.TOKEN_SECRET || '',
    WECHAT_API_SECRET: backendConfig.TOKEN_SECRET || '',

    // 企业微信配置
    WECHAT_CORP_ID: backendConfig.WECHAT_CORP_ID || '',
    WECHAT_CORP_SECRET: backendConfig.WECHAT_CORP_SECRET || '',
    WECHAT_AGENT_ID: backendConfig.WECHAT_AGENT_ID || '',

    // 绑定密钥
    WECHAT_BINDING_SECRET: backendConfig.BINDING_SECRET || '',

    // 极光推送配置
    JPUSH_APP_KEY: backendConfig.JPUSH_APP_KEY || '',
    JPUSH_APP_SECRET: backendConfig.JPUSH_MASTER_SECRET || '',

    // 调试配置
    DEBUG_MODE: backendConfig.DEBUG === 'true' ? 'true' : 'false',
    LOG_LEVEL: backendConfig.LOG_LEVEL || 'info',
    NODE_ENV: backendConfig.NODE_ENV || 'production',
  };

  return mobileConfig;
}

// 生成.env文件内容
function generateEnvFileContent(config) {
  const lines = [
    '# 公职猫微信转发功能 - 生产环境配置',
    '# 此文件由 sync-production-config.js 自动生成',
    '# 请勿手动修改，运行 yarn wechat:sync-config 重新生成',
    '',
    '# 微信转发API服务地址（腾讯云函数）',
    `WECHAT_API_BASE_URL=${config.WECHAT_API_BASE_URL}`,
    '',
    '# API访问令牌和签名密钥',
    `WECHAT_API_TOKEN=${config.WECHAT_API_TOKEN}`,
    `WECHAT_API_SECRET=${config.WECHAT_API_SECRET}`,
    '',
    '# 企业微信配置',
    `WECHAT_CORP_ID=${config.WECHAT_CORP_ID}`,
    `WECHAT_CORP_SECRET=${config.WECHAT_CORP_SECRET}`,
    `WECHAT_AGENT_ID=${config.WECHAT_AGENT_ID}`,
    '',
    '# 绑定密钥',
    `WECHAT_BINDING_SECRET=${config.WECHAT_BINDING_SECRET}`,
    '',
    '# 极光推送配置',
    `JPUSH_APP_KEY=${config.JPUSH_APP_KEY}`,
    `JPUSH_APP_SECRET=${config.JPUSH_APP_SECRET}`,
    '',
    '# 调试配置',
    `DEBUG_MODE=${config.DEBUG_MODE}`,
    `LOG_LEVEL=${config.LOG_LEVEL}`,
    `NODE_ENV=${config.NODE_ENV}`,
    '',
    '# 注意：',
    '# 1. 此配置文件包含敏感信息，请勿提交到版本控制系统',
    '# 2. 生产环境部署时，这些配置应通过安全的方式管理',
    '# 3. 如需更新配置，请修改后端配置后重新运行同步脚本',
  ];

  return lines.join('\n');
}

// 验证配置完整性
function validateConfig(config) {
  const requiredFields = [
    'WECHAT_API_BASE_URL',
    'WECHAT_API_TOKEN',
    'WECHAT_CORP_ID',
    'WECHAT_CORP_SECRET',
    'JPUSH_APP_KEY',
    'WECHAT_BINDING_SECRET',
  ];

  const missingFields = requiredFields.filter(field => !config[field]);

  if (missingFields.length > 0) {
    throw new Error(`缺少必要的配置项: ${missingFields.join(', ')}`);
  }

  // 验证配置格式
  const validations = [
    {
      field: 'WECHAT_CORP_ID',
      pattern: /^ww[a-f0-9]{16}$/,
      message: '企业微信CorpID格式不正确',
    },
    {
      field: 'JPUSH_APP_KEY',
      pattern: /^[a-f0-9]{24}$/,
      message: '极光推送AppKey格式不正确',
    },
    {
      field: 'WECHAT_API_BASE_URL',
      pattern: /^https:\/\/.+/,
      message: 'API地址必须是HTTPS',
    },
  ];

  for (const validation of validations) {
    const value = config[validation.field];
    if (value && !validation.pattern.test(value)) {
      throw new Error(`${validation.message}: ${value}`);
    }
  }
}

// 备份现有配置
function backupExistingConfig() {
  const envPath = path.join(__dirname, '../.env');
  const backupPath = path.join(__dirname, '../.env.backup.' + Date.now());

  if (fs.existsSync(envPath)) {
    fs.copyFileSync(envPath, backupPath);
    logInfo(`已备份现有配置至: ${backupPath}`);
    return backupPath;
  }

  return null;
}

// 更新appConfig.ts中的配置
function updateAppConfig(config) {
  const appConfigPath = path.join(__dirname, '../src/config/appConfig.ts');

  if (!fs.existsSync(appConfigPath)) {
    logWarning('appConfig.ts 文件不存在，跳过更新');
    return;
  }

  let content = fs.readFileSync(appConfigPath, 'utf8');

  // 更新极光推送配置
  const jpushAppKeyPattern = /appKey:\s*['"]([^'"]+)['"]/;
  if (jpushAppKeyPattern.test(content)) {
    content = content.replace(jpushAppKeyPattern, `appKey: '${config.JPUSH_APP_KEY}'`);
    logInfo('已更新appConfig.ts中的极光推送AppKey');
  }

  // 更新生产环境标识
  const productionPattern = /production:\s*(true|false)/;
  if (productionPattern.test(content)) {
    content = content.replace(productionPattern, `production: ${config.NODE_ENV === 'production'}`);
    logInfo('已更新appConfig.ts中的生产环境标识');
  }

  fs.writeFileSync(appConfigPath, content);
}

// 主函数
async function main() {
  logHeader('生产环境配置同步');

  try {
    // 1. 读取后端配置
    logInfo('正在读取后端配置...');
    const backendConfig = readBackendConfig();
    logSuccess(`已读取后端配置，包含 ${Object.keys(backendConfig).length} 个配置项`);

    // 2. 生成移动端配置
    logInfo('正在生成移动端配置...');
    const mobileConfig = generateMobileConfig(backendConfig);

    // 3. 验证配置完整性
    logInfo('正在验证配置完整性...');
    validateConfig(mobileConfig);
    logSuccess('配置验证通过');

    // 4. 备份现有配置
    logInfo('正在备份现有配置...');
    const backupPath = backupExistingConfig();

    // 5. 生成新的.env文件
    logInfo('正在生成新的.env文件...');
    const envContent = generateEnvFileContent(mobileConfig);
    const envPath = path.join(__dirname, '../.env');
    fs.writeFileSync(envPath, envContent);
    logSuccess(`已生成新的.env文件: ${envPath}`);

    // 6. 更新appConfig.ts
    logInfo('正在更新appConfig.ts...');
    updateAppConfig(mobileConfig);

    // 7. 显示同步结果
    logHeader('配置同步完成');
    logSuccess('✅ 后端配置已成功同步到移动端');
    logInfo('同步的配置项:');
    Object.keys(mobileConfig).forEach(key => {
      const value = mobileConfig[key];
      const displayValue = key.includes('SECRET') || key.includes('TOKEN')
        ? value.substring(0, 8) + '...'
        : value;
      logInfo(`  ${key}: ${displayValue}`);
    });

    logInfo('\n下一步操作:');
    logInfo('1. 运行 yarn wechat:test-production-integration 测试真实环境连接');
    logInfo('2. 在真机上测试完整的微信转发功能');
    logInfo('3. 如有问题，可从备份文件恢复配置');

    if (backupPath) {
      logInfo(`4. 备份文件位置: ${backupPath}`);
    }

  } catch (error) {
    logError(`配置同步失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { generateMobileConfig, validateConfig };
