/**
 * 文件错误对话框组件
 * 提供温和专业的政务应用错误提示
 */

import React from 'react';
import { Alert, Platform } from 'react-native';

export interface FileErrorDialogOptions {
  fileName: string;
  errorType: 'missing' | 'corrupted' | 'access_denied' | 'unknown';
  onReupload?: () => void;
  onDismiss?: () => void;
  showReuploadOption?: boolean;
}

/**
 * 文件错误对话框类
 */
export class FileErrorDialog {

  /**
   * 显示文件丢失错误对话框
   */
  static showFileMissingDialog(options: FileErrorDialogOptions): void {
    const { fileName, onReupload, onDismiss, showReuploadOption = true } = options;

    const title = '文件暂时无法访问';
    const message = this.getFileMissingMessage(fileName);
    const buttons = this.getFileMissingButtons(showReuploadOption, onReupload, onDismiss);

    Alert.alert(title, message, buttons);
  }

  /**
   * 显示文件损坏错误对话框
   */
  static showFileCorruptedDialog(options: FileErrorDialogOptions): void {
    const { fileName, onReupload, onDismiss, showReuploadOption = true } = options;

    const title = '文件格式异常';
    const message = this.getFileCorruptedMessage(fileName);
    const buttons = this.getFileCorruptedButtons(showReuploadOption, onReupload, onDismiss);

    Alert.alert(title, message, buttons);
  }

  /**
   * 显示访问权限错误对话框
   */
  static showAccessDeniedDialog(options: FileErrorDialogOptions): void {
    const { fileName, onDismiss } = options;

    const title = '文件访问受限';
    const message = this.getAccessDeniedMessage(fileName);
    const buttons = this.getAccessDeniedButtons(onDismiss);

    Alert.alert(title, message, buttons);
  }

  /**
   * 显示通用文件错误对话框
   */
  static showGenericFileError(options: FileErrorDialogOptions): void {
    const { fileName, errorType, onReupload, onDismiss, showReuploadOption = true } = options;

    switch (errorType) {
      case 'missing':
        this.showFileMissingDialog(options);
        break;
      case 'corrupted':
        this.showFileCorruptedDialog(options);
        break;
      case 'access_denied':
        this.showAccessDeniedDialog(options);
        break;
      default:
        this.showUnknownErrorDialog(options);
        break;
    }
  }

  /**
   * 显示未知错误对话框
   */
  static showUnknownErrorDialog(options: FileErrorDialogOptions): void {
    const { fileName, onReupload, onDismiss, showReuploadOption = true } = options;

    const title = '文件处理异常';
    const message = this.getUnknownErrorMessage(fileName);
    const buttons = this.getUnknownErrorButtons(showReuploadOption, onReupload, onDismiss);

    Alert.alert(title, message, buttons);
  }

  /**
   * 获取文件丢失的友好提示信息
   */
  private static getFileMissingMessage(fileName: string): string {
    return `很抱歉，文件"${fileName}"暂时无法访问。\n\n` +
           '这可能是由于应用更新或系统维护导致的。为了确保您的工作不受影响，' +
           '建议您重新上传该文件。\n\n' +
           '如果您需要帮助，请联系技术支持。';
  }

  /**
   * 获取文件损坏的友好提示信息
   */
  private static getFileCorruptedMessage(fileName: string): string {
    return `文件"${fileName}"的格式可能存在异常，无法正常打开。\n\n` +
           '这可能是由于文件传输过程中的问题导致的。' +
           '建议您重新上传原始文件。\n\n' +
           '如果问题持续存在，请检查文件是否完整。';
  }

  /**
   * 获取访问权限错误的友好提示信息
   */
  private static getAccessDeniedMessage(fileName: string): string {
    return `当前无法访问文件"${fileName}"。\n\n` +
           '这可能是由于系统权限设置导致的。' +
           '请稍后重试，或联系系统管理员获取帮助。';
  }

  /**
   * 获取未知错误的友好提示信息
   */
  private static getUnknownErrorMessage(fileName: string): string {
    return `处理文件"${fileName}"时遇到了意外问题。\n\n` +
           '我们正在努力解决这个问题。' +
           '您可以尝试重新上传文件，或稍后再试。\n\n' +
           '如果问题持续存在，请联系技术支持。';
  }

  /**
   * 获取文件丢失对话框的按钮配置
   */
  private static getFileMissingButtons(
    showReuploadOption: boolean,
    onReupload?: () => void,
    onDismiss?: () => void
  ) {
    const buttons = [];

    if (showReuploadOption && onReupload) {
      buttons.push({
        text: '重新上传',
        style: 'default' as const,
        onPress: () => {
          console.log('[FileErrorDialog] 用户选择重新上传文件');
          onReupload();
        },
      });
    }

    buttons.push({
      text: '知道了',
      style: 'cancel' as const,
      onPress: () => {
        console.log('[FileErrorDialog] 用户确认文件丢失提示');
        onDismiss?.();
      },
    });

    return buttons;
  }

  /**
   * 获取文件损坏对话框的按钮配置
   */
  private static getFileCorruptedButtons(
    showReuploadOption: boolean,
    onReupload?: () => void,
    onDismiss?: () => void
  ) {
    const buttons = [];

    if (showReuploadOption && onReupload) {
      buttons.push({
        text: '重新上传',
        style: 'default' as const,
        onPress: () => {
          console.log('[FileErrorDialog] 用户选择重新上传损坏文件');
          onReupload();
        },
      });
    }

    buttons.push({
      text: '稍后处理',
      style: 'cancel' as const,
      onPress: () => {
        console.log('[FileErrorDialog] 用户选择稍后处理损坏文件');
        onDismiss?.();
      },
    });

    return buttons;
  }

  /**
   * 获取访问权限错误对话框的按钮配置
   */
  private static getAccessDeniedButtons(onDismiss?: () => void) {
    return [
      {
        text: '稍后重试',
        style: 'default' as const,
        onPress: () => {
          console.log('[FileErrorDialog] 用户选择稍后重试访问文件');
          onDismiss?.();
        },
      },
      {
        text: '知道了',
        style: 'cancel' as const,
        onPress: () => {
          console.log('[FileErrorDialog] 用户确认访问权限提示');
          onDismiss?.();
        },
      },
    ];
  }

  /**
   * 获取未知错误对话框的按钮配置
   */
  private static getUnknownErrorButtons(
    showReuploadOption: boolean,
    onReupload?: () => void,
    onDismiss?: () => void
  ) {
    const buttons = [];

    if (showReuploadOption && onReupload) {
      buttons.push({
        text: '重新上传',
        style: 'default' as const,
        onPress: () => {
          console.log('[FileErrorDialog] 用户选择重新上传文件（未知错误）');
          onReupload();
        },
      });
    }

    buttons.push({
      text: '稍后重试',
      style: 'default' as const,
      onPress: () => {
        console.log('[FileErrorDialog] 用户选择稍后重试（未知错误）');
        onDismiss?.();
      },
    });

    buttons.push({
      text: '取消',
      style: 'cancel' as const,
      onPress: () => {
        console.log('[FileErrorDialog] 用户取消操作（未知错误）');
        onDismiss?.();
      },
    });

    return buttons;
  }

  /**
   * 根据错误类型自动选择合适的对话框
   */
  static showAppropriateDialog(
    fileName: string,
    error: Error,
    options: {
      onReupload?: () => void;
      onDismiss?: () => void;
      showReuploadOption?: boolean;
    } = {}
  ): void {
    const errorMessage = error.message.toLowerCase();
    let errorType: FileErrorDialogOptions['errorType'] = 'unknown';

    // 根据错误信息判断错误类型
    if (errorMessage.includes('not found') ||
        errorMessage.includes('does not exist') ||
        errorMessage.includes('no such file')) {
      errorType = 'missing';
    } else if (errorMessage.includes('corrupted') ||
               errorMessage.includes('invalid format') ||
               errorMessage.includes('parse error')) {
      errorType = 'corrupted';
    } else if (errorMessage.includes('permission') ||
               errorMessage.includes('access denied') ||
               errorMessage.includes('unauthorized')) {
      errorType = 'access_denied';
    }

    this.showGenericFileError({
      fileName,
      errorType,
      ...options,
    });
  }
}

/**
 * React Hook for file error handling
 */
export const useFileErrorHandler = () => {
  const showFileError = (
    fileName: string,
    error: Error,
    options: {
      onReupload?: () => void;
      onDismiss?: () => void;
      showReuploadOption?: boolean;
    } = {}
  ) => {
    FileErrorDialog.showAppropriateDialog(fileName, error, options);
  };

  return { showFileError };
};
