import { useState, useEffect } from 'react';
import type { Message as ChatMessageType } from '../utils/chat';
import { addMessage } from '../db/messageService';
import { getWeChatSyncService } from '../services/WeChatServiceManager';

// MessageModel 类型定义为 any，避免找不到类型错误

type MessageModel = any;

interface UseChatMessagesParams {
  getAllUserAppMessages: () => Promise<MessageModel[]>;
  parseExt: (ext: any) => any;
  parseParticipants: (p: any) => string[] | undefined;
}

export function useChatMessages({
  getAllUserAppMessages,
  parseExt,
  parseParticipants,
}: UseChatMessagesParams) {
  const [messages, setMessages] = useState<ChatMessageType[]>([]);

  // 初始化加载消息
  useEffect(() => {
    let isMounted = true;
    (async () => {
      const dbMessages: MessageModel[] = await getAllUserAppMessages();
      if (!isMounted) {return;}
      const uiMessages: ChatMessageType[] = dbMessages.map((msg: MessageModel) => {
        const ext: any = parseExt(msg.ext);
        const participants: string[] | undefined = parseParticipants(msg.participants);
        let status: 'sent' | 'failed' | 'sending' | 'received' | undefined;
        if (msg.status === 'sent' || msg.status === 'failed' || msg.status === 'pending' || msg.status === 'received') {
          status = msg.status === 'pending' ? 'sending' : msg.status;
        } else {
          status = undefined;
        }
        const sender: 'user' | 'assistant' = (msg.sender === 'user' || msg.sender === 'assistant') ? msg.sender as 'user' | 'assistant' : 'user';
        const type: 'text' | 'image' | 'audio' | 'card' | 'url' | 'file' = (['text','image','audio','card','url','file'].includes(msg.type) ? msg.type as ChatMessageType['type'] : 'text');
        return {
          id: msg.id,
          text: msg.content,
          createdAt: new Date(msg.createdAt),
          sender,
          type,
          status,
          mediaUris: ext?.mediaUris,
          audioData: ext?.audioData,
          cardData: ext?.cardData,
          urlData: ext?.urlData,
          fileData: ext?.fileData,
          participants: JSON.stringify(participants),
        };
      });
      setMessages(uiMessages.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()));
    })();
    return () => { isMounted = false; };
  }, [getAllUserAppMessages, parseExt, parseParticipants]);

  // 主动刷新消息列表
  const refreshMessages = async () => {
    try {
      // 1. 先触发微信消息同步
      console.log('[useChatMessages] 触发微信消息同步...');
      const syncService = getWeChatSyncService();
      if (syncService) {
        await syncService.performSyncWithRetry('manual_refresh');
        console.log('[useChatMessages] 微信消息同步完成');
      } else {
        console.log('[useChatMessages] 微信同步服务未初始化，跳过同步');
      }
    } catch (error) {
      console.warn('[useChatMessages] 微信消息同步失败:', error);
      // 同步失败不影响本地消息刷新
    }

    // 2. 刷新本地消息列表
    const dbMessages = await getAllUserAppMessages();
    const uiMessages: ChatMessageType[] = dbMessages.map((msg: MessageModel) => {
      const ext: any = parseExt(msg.ext);
      const participants: string[] | undefined = parseParticipants(msg.participants);
      let status: 'sent' | 'failed' | 'sending' | 'received' | undefined;
      if (msg.status === 'sent' || msg.status === 'failed' || msg.status === 'pending' || msg.status === 'received') {
        status = msg.status === 'pending' ? 'sending' : msg.status;
      } else {
        status = undefined;
      }
      const sender: 'user' | 'assistant' = (msg.sender === 'user' || msg.sender === 'assistant') ? msg.sender as 'user' | 'assistant' : 'user';
      const type: 'text' | 'image' | 'audio' | 'card' | 'url' | 'file' = (['text','image','audio','card','url','file'].includes(msg.type) ? msg.type as ChatMessageType['type'] : 'text');
      return {
        id: msg.id,
        text: msg.content,
        createdAt: new Date(msg.createdAt),
        sender,
        type,
        status,
        mediaUris: ext?.mediaUris,
        audioData: ext?.audioData,
        cardData: ext?.cardData,
        urlData: ext?.urlData,
        fileData: ext?.fileData,
        participants: JSON.stringify(participants),
      };
    });
    setMessages(uiMessages.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()));
  };

  // 新增 handleSend 统一消息发送逻辑
  const handleSend = async (inputText: string) => {
    const text = inputText.trim();
    if (!text) {
      return;
    }

    // URL和域名的正则表达式
    const urlRegex = /(https?:\/\/[\w\-.?&=/#%:;,@!$'()*+~]+)/;
    const domainRegex = /^(?:www\.)?([\w-]+\.[\w.-]+)$/i;

    const urlMatch = text.match(urlRegex);
    const domainMatch = text.match(domainRegex);

    let isPureLink = false;
    let linkToProcess = '';

    if (urlMatch && text.trim() === urlMatch[0]) {
      isPureLink = true;
      linkToProcess = urlMatch[0];
    } else if (domainMatch) {
      isPureLink = true;
      // 为白名单域名添加http，其他添加https
      const domain = domainMatch[1].toLowerCase();
      if (domain === 'gongzhimall.com' || domain === 'xhslink.com') {
        linkToProcess = `http://${text}`;
      } else {
        linkToProcess = `https://${text}`;
      }
    }

    if (isPureLink) {
      try {
        // 尝试抓取网页信息生成卡片
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const resp = await fetch(linkToProcess, {
          signal: controller.signal,
          headers: { 'User-Agent': 'Mozilla/5.0 (compatible; GongZhiMall/1.0)' },
        });
        clearTimeout(timeoutId);

        if (!resp.ok) { throw new Error(`HTTP error! status: ${resp.status}`); }
        const contentType = resp.headers.get('content-type');
        if (!contentType || !contentType.includes('text/html')) {
          throw new Error('Content is not HTML');
        }

        const html = await resp.text();
        const titleMatch = html.match(/<title>(.*?)<\/title>/i);
        const descMatch = html.match(/<meta[^>]+(?:name=["']description["']|property=["']og:description["'])[^>]+content=["']([^"']+)["'][^>]*>/i);
        const imgMatch = html.match(/<meta[^>]+property=["']og:image["'][^>]+content=["']([^"']+)["'][^>]*>/i);

        const urlData = {
          url: linkToProcess,
          title: titleMatch ? titleMatch[1].trim() : text,
          description: descMatch ? descMatch[1].trim() : '',
          image: imgMatch ? imgMatch[1].trim() : '',
        };

        await addMessage({
          type: 'url',
          content: text,
          ext: JSON.stringify({ urlData }),
          createdAt: Date.now(),
          sender: 'user',
          participants: JSON.stringify(['user', 'assistant']),
          sessionId: 'default',
        });
      } catch (error) {
        console.warn('URL预览抓取失败，降级为无预览卡片:', error);
        // 即使抓取失败，也作为url类型消息存储
        const urlData = { url: linkToProcess, title: text, description: '', image: '' };
        await addMessage({
          type: 'url',
          content: text,
          ext: JSON.stringify({ urlData }),
          createdAt: Date.now(),
          sender: 'user',
          participants: JSON.stringify(['user', 'assistant']),
          sessionId: 'default',
        });
      }
      await refreshMessages();
      return;
    }

    // 对于混合文本，作为普通文本消息处理
    // UI层将使用 ParsedText 自动识别并使链接可点击
    await addMessage({
      type: 'text',
      content: text,
      createdAt: Date.now(),
      sender: 'user',
      participants: JSON.stringify(['user', 'assistant']),
      sessionId: 'default',
    });
    await refreshMessages();
  };

  return { messages, setMessages, refreshMessages, handleSend };
}
