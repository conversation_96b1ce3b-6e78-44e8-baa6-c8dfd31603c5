/**
 * 访问令牌管理器
 * 管理企业微信访问令牌的获取、缓存和自动刷新
 * 支持令牌过期自动刷新和并发请求合并
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import WeChatAPIService from './WeChatAPIService';

export interface AccessTokenInfo {
  accessToken: string;
  expiresIn: number;
  expiresAt: number;
  refreshAt: number; // 提前刷新时间
}

export interface TokenRefreshResult {
  success: boolean;
  accessToken?: string;
  expiresIn?: number;
  error?: string;
}

class AccessTokenManager {
  private static readonly STORAGE_KEY = 'wechat_access_token_info';
  private static readonly REFRESH_BUFFER_TIME = 5 * 60 * 1000; // 提前5分钟刷新
  private static readonly MAX_RETRY_COUNT = 3;
  private static readonly RETRY_DELAY = 1000;

  private currentTokenInfo: AccessTokenInfo | null = null;
  private refreshPromise: Promise<TokenRefreshResult> | null = null;
  private refreshTimer: number | null = null;

  constructor() {
    this.initializeTokenManager();
  }

  /**
   * 初始化令牌管理器
   */
  private async initializeTokenManager(): Promise<void> {
    try {
      // 从本地存储恢复令牌信息
      await this.loadTokenFromStorage();

      // 如果有有效令牌，设置自动刷新
      if (this.currentTokenInfo) {
        this.scheduleTokenRefresh();
      }

      console.log('[AccessTokenManager] 令牌管理器初始化完成');
    } catch (error) {
      console.error('[AccessTokenManager] 初始化失败:', error);
    }
  }

  /**
   * 获取有效的访问令牌
   */
  async getValidAccessToken(): Promise<string | null> {
    try {
      // 检查当前令牌是否有效
      if (this.isTokenValid()) {
        return this.currentTokenInfo!.accessToken;
      }

      // 如果正在刷新，等待刷新完成
      if (this.refreshPromise) {
        console.log('[AccessTokenManager] 等待令牌刷新完成...');
        const refreshResult = await this.refreshPromise;
        if (refreshResult.success) {
          return refreshResult.accessToken!;
        }
      }

      // 执行令牌刷新
      const refreshResult = await this.refreshToken();
      if (refreshResult.success) {
        return refreshResult.accessToken!;
      }

      console.error('[AccessTokenManager] 获取访问令牌失败:', refreshResult.error);
      return null;
    } catch (error) {
      console.error('[AccessTokenManager] 获取访问令牌异常:', error);
      return null;
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(): Promise<TokenRefreshResult> {
    // 防止并发刷新
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    console.log('[AccessTokenManager] 开始刷新访问令牌...');

    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 执行令牌刷新操作
   */
  private async performTokenRefresh(): Promise<TokenRefreshResult> {
    let retryCount = 0;
    let lastError: string = '';

    while (retryCount < AccessTokenManager.MAX_RETRY_COUNT) {
      try {
        // 调用API获取新的访问令牌
        const response = await WeChatAPIService.getAccessToken();

        if (!response.success || !response.data) {
          throw new Error(response.error || '获取访问令牌失败');
        }

        const { access_token, expires_in } = response.data;
        const now = Date.now();

        // 创建新的令牌信息
        const newTokenInfo: AccessTokenInfo = {
          accessToken: access_token,
          expiresIn: expires_in,
          expiresAt: now + (expires_in * 1000),
          refreshAt: now + ((expires_in - AccessTokenManager.REFRESH_BUFFER_TIME / 1000) * 1000),
        };

        // 保存令牌信息
        await this.saveTokenInfo(newTokenInfo);

        // 设置自动刷新
        this.scheduleTokenRefresh();

        console.log('[AccessTokenManager] 访问令牌刷新成功:', {
          expiresIn: expires_in,
          expiresAt: new Date(newTokenInfo.expiresAt).toLocaleString(),
          refreshAt: new Date(newTokenInfo.refreshAt).toLocaleString(),
        });

        return {
          success: true,
          accessToken: access_token,
          expiresIn: expires_in,
        };

      } catch (error) {
        retryCount++;
        lastError = error instanceof Error ? error.message : String(error);

        console.warn(`[AccessTokenManager] 刷新失败 (${retryCount}/${AccessTokenManager.MAX_RETRY_COUNT}):`, lastError);

        // 等待后重试
        if (retryCount < AccessTokenManager.MAX_RETRY_COUNT) {
          await new Promise(resolve => setTimeout(resolve, AccessTokenManager.RETRY_DELAY * retryCount));
        }
      }
    }

    return {
      success: false,
      error: lastError,
    };
  }

  /**
   * 保存令牌信息
   */
  private async saveTokenInfo(tokenInfo: AccessTokenInfo): Promise<void> {
    try {
      this.currentTokenInfo = tokenInfo;
      await AsyncStorage.setItem(AccessTokenManager.STORAGE_KEY, JSON.stringify(tokenInfo));
      console.log('[AccessTokenManager] 令牌信息已保存');
    } catch (error) {
      console.error('[AccessTokenManager] 保存令牌信息失败:', error);
    }
  }

  /**
   * 从本地存储加载令牌信息
   */
  private async loadTokenFromStorage(): Promise<void> {
    try {
      const tokenInfoStr = await AsyncStorage.getItem(AccessTokenManager.STORAGE_KEY);
      if (tokenInfoStr) {
        const tokenInfo: AccessTokenInfo = JSON.parse(tokenInfoStr);

        // 检查令牌是否过期
        if (tokenInfo.expiresAt > Date.now()) {
          this.currentTokenInfo = tokenInfo;
          console.log('[AccessTokenManager] 从本地存储恢复令牌信息:', {
            expiresAt: new Date(tokenInfo.expiresAt).toLocaleString(),
            refreshAt: new Date(tokenInfo.refreshAt).toLocaleString(),
          });
        } else {
          console.log('[AccessTokenManager] 本地令牌已过期，将重新获取');
          await this.clearTokenInfo();
        }
      }
    } catch (error) {
      console.error('[AccessTokenManager] 加载令牌信息失败:', error);
    }
  }

  /**
   * 检查令牌是否有效
   */
  private isTokenValid(): boolean {
    if (!this.currentTokenInfo) {
      return false;
    }

    const now = Date.now();
    return now < this.currentTokenInfo.expiresAt;
  }

  /**
   * 检查是否需要刷新令牌
   */
  private shouldRefreshToken(): boolean {
    if (!this.currentTokenInfo) {
      return true;
    }

    const now = Date.now();
    return now >= this.currentTokenInfo.refreshAt;
  }

  /**
   * 安排令牌自动刷新
   */
  private scheduleTokenRefresh(): void {
    // 清除现有定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    if (!this.currentTokenInfo) {
      return;
    }

    const now = Date.now();
    const refreshTime = this.currentTokenInfo.refreshAt;

    if (refreshTime <= now) {
      // 立即刷新
      this.refreshToken().catch(error => {
        console.error('[AccessTokenManager] 自动刷新失败:', error);
      });
    } else {
      // 安排定时刷新
      const delay = refreshTime - now;
      this.refreshTimer = setTimeout(() => {
        this.refreshToken().catch(error => {
          console.error('[AccessTokenManager] 定时刷新失败:', error);
        });
      }, delay);

      console.log('[AccessTokenManager] 已安排自动刷新:', {
        delay: Math.round(delay / 1000) + '秒',
        refreshTime: new Date(refreshTime).toLocaleString(),
      });
    }
  }

  /**
   * 清除令牌信息
   */
  async clearTokenInfo(): Promise<void> {
    try {
      this.currentTokenInfo = null;

      // 清除定时器
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }

      // 清除本地存储
      await AsyncStorage.removeItem(AccessTokenManager.STORAGE_KEY);

      console.log('[AccessTokenManager] 令牌信息已清除');
    } catch (error) {
      console.error('[AccessTokenManager] 清除令牌信息失败:', error);
    }
  }

  /**
   * 强制刷新令牌（忽略缓存）
   */
  async forceRefreshToken(): Promise<TokenRefreshResult> {
    console.log('[AccessTokenManager] 强制刷新令牌...');

    // 清除当前令牌信息
    await this.clearTokenInfo();

    // 执行刷新
    return this.refreshToken();
  }

  /**
   * 获取令牌状态信息
   */
  getTokenStatus(): {
    hasToken: boolean;
    isValid: boolean;
    shouldRefresh: boolean;
    expiresAt?: string;
    refreshAt?: string;
    remainingTime?: number;
  } {
    if (!this.currentTokenInfo) {
      return {
        hasToken: false,
        isValid: false,
        shouldRefresh: true,
      };
    }

    const now = Date.now();
    const isValid = this.isTokenValid();
    const shouldRefresh = this.shouldRefreshToken();
    const remainingTime = Math.max(0, this.currentTokenInfo.expiresAt - now);

    return {
      hasToken: true,
      isValid,
      shouldRefresh,
      expiresAt: new Date(this.currentTokenInfo.expiresAt).toLocaleString(),
      refreshAt: new Date(this.currentTokenInfo.refreshAt).toLocaleString(),
      remainingTime,
    };
  }

  /**
   * 预热令牌（确保有有效令牌）
   */
  async warmupToken(): Promise<boolean> {
    try {
      const token = await this.getValidAccessToken();
      return token !== null;
    } catch (error) {
      console.error('[AccessTokenManager] 预热令牌失败:', error);
      return false;
    }
  }

  /**
   * 监听令牌状态变化
   */
  onTokenStatusChange(callback: (status: {
    hasToken: boolean;
    isValid: boolean;
    shouldRefresh: boolean;
  }) => void): () => void {
    const checkInterval = setInterval(() => {
      const status = this.getTokenStatus();
      callback({
        hasToken: status.hasToken,
        isValid: status.isValid,
        shouldRefresh: status.shouldRefresh,
      });
    }, 60000); // 每分钟检查一次

    // 返回取消监听的函数
    return () => {
      clearInterval(checkInterval);
    };
  }

  /**
   * 销毁令牌管理器
   */
  destroy(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    this.refreshPromise = null;
    this.currentTokenInfo = null;

    console.log('[AccessTokenManager] 令牌管理器已销毁');
  }
}

export default new AccessTokenManager();
