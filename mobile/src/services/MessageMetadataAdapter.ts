/**
 * 消息元数据适配器
 * 处理新的消息格式和元数据结构转换
 * 适配零存储架构下的消息数据格式
 */

import { WeChatMessage } from './WeChatAPIService';

// 本地消息格式
export interface LocalMessage {
  sessionId: string;
  content: string;
  type: 'text' | 'image' | 'audio' | 'card' | 'url' | 'file';
  sender: 'user' | 'assistant';
  status: 'sending' | 'sent' | 'received' | 'failed';
  createdAt: number;
  ext?: string; // JSON字符串，存储扩展信息
  participants?: string; // JSON字符串，存储参与者信息
}

// 扩展信息结构
export interface MessageExtension {
  wechatMessageId: string | number;
  messageType: string;
  mediaId?: string;
  mediaIdExpiresAt?: string;
  senderName?: string;
  senderId?: string;
  chatName?: string;
  isGroup?: boolean;
  metadata?: any;
  // 媒体文件信息
  mediaInfo?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    localPath?: string;
    downloadStatus?: 'pending' | 'downloading' | 'completed' | 'failed';
  };
}

// 消息处理结果
export interface MessageProcessResult {
  success: boolean;
  localMessage?: LocalMessage;
  error?: string;
  needsMediaDownload?: boolean;
  mediaDownloadInfo?: {
    mediaId: string;
    mediaType: 'image' | 'voice' | 'video' | 'file';
    fileName?: string;
  } | null;
}

class MessageMetadataAdapter {
  /**
   * 将微信消息转换为本地消息格式
   */
  adaptWeChatMessage(wechatMessage: WeChatMessage): MessageProcessResult {
    try {
      console.log('[MessageAdapter] 开始适配微信消息:', wechatMessage.id);

      // 验证消息数据
      if (!this.validateWeChatMessage(wechatMessage)) {
        return {
          success: false,
          error: '微信消息数据验证失败',
        };
      }

      // 提取基本信息
      const messageId = wechatMessage.wechat_message_id || wechatMessage.id;
      const messageType = wechatMessage.message_type || wechatMessage.type || 'text';
      const timestamp = wechatMessage.metadata?.timestamp || wechatMessage.timestamp || Date.now();
      const senderName = wechatMessage.metadata?.from_user || wechatMessage.sender?.name || 'Unknown';
      const senderId = wechatMessage.metadata?.from_user || wechatMessage.sender?.id || 'unknown';

      // 构建扩展信息
      const extension: MessageExtension = {
        wechatMessageId: messageId,
        messageType,
        mediaId: wechatMessage.media_id,
        mediaIdExpiresAt: wechatMessage.media_id_expires_at,
        senderName,
        senderId,
        chatName: wechatMessage.chatName || 'WeChat',
        isGroup: wechatMessage.isGroup || false,
        metadata: wechatMessage.metadata,
      };

      // 处理媒体文件
      const mediaDownloadInfo = this.extractMediaDownloadInfo(wechatMessage);
      if (mediaDownloadInfo) {
        extension.mediaInfo = {
          fileName: wechatMessage.metadata?.file_name,
          fileSize: wechatMessage.metadata?.file_size,
          downloadStatus: 'pending',
        };
      }

      // 生成消息内容
      const content = this.generateMessageContent(wechatMessage);

      // 构建本地消息
      const localMessage: LocalMessage = {
        sessionId: this.generateSessionId(wechatMessage),
        content,
        type: this.mapMessageType(messageType, wechatMessage),
        sender: 'user', // 微信转发的消息都标记为用户消息
        status: 'received',
        createdAt: timestamp,
        ext: JSON.stringify(extension),
        participants: JSON.stringify(['user', 'assistant']),
      };

      // 为图片和文件消息添加必要的显示字段
      this.addDisplayFieldsForMediaMessage(localMessage, wechatMessage, extension);

      console.log('[MessageAdapter] 微信消息适配完成:', {
        messageId,
        messageType,
        hasMedia: !!mediaDownloadInfo,
        contentLength: content.length,
      });

      return {
        success: true,
        localMessage,
        needsMediaDownload: !!mediaDownloadInfo,
        mediaDownloadInfo,
      };

    } catch (error) {
      console.error('[MessageAdapter] 适配微信消息失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      };
    }
  }

  /**
   * 为媒体消息添加显示字段
   */
  private addDisplayFieldsForMediaMessage(localMessage: LocalMessage, wechatMessage: WeChatMessage, extension: any): void {
    const messageType = wechatMessage.message_type || wechatMessage.type || 'text';

    switch (messageType) {
      case 'image':
        // 为图片消息添加占位符URI，等待下载完成后更新
        const imageExtension = JSON.parse(localMessage.ext || '{}');
        imageExtension.mediaUris = [`wechat_image_placeholder_${wechatMessage.media_id}`];
        imageExtension.downloadPending = true;
        localMessage.ext = JSON.stringify(imageExtension);
        break;

      case 'file':
        // 为文件消息添加文件信息
        const fileExtension = JSON.parse(localMessage.ext || '{}');
        const fileName = wechatMessage.metadata?.file_name || `file_${Date.now()}`;

        // 优先使用服务器端检测到的文件类型信息
        const detectedMimeType = wechatMessage.mime_type || wechatMessage.metadata?.detected_type;
        const fileType = detectedMimeType || this.getMimeTypeFromFileName(fileName);

        fileExtension.fileData = {
          uri: `wechat_file_placeholder_${wechatMessage.media_id}`,
          name: fileName,
          size: wechatMessage.metadata?.file_size || 0,
          type: fileType,
          downloadPending: true,
          // 添加服务器端检测到的额外信息
          extension: wechatMessage.file_extension,
          detectedType: wechatMessage.metadata?.detected_type,
          originalMimeType: wechatMessage.original_mime_type
        };
        localMessage.ext = JSON.stringify(fileExtension);
        break;

      case 'voice':
        // 为语音消息添加音频信息
        const audioExtension = JSON.parse(localMessage.ext || '{}');
        audioExtension.audioData = {
          uri: `wechat_audio_placeholder_${wechatMessage.media_id}`,
          duration: wechatMessage.metadata?.duration || 0,
          downloadPending: true
        };
        localMessage.ext = JSON.stringify(audioExtension);
        break;
    }
  }

  /**
   * 验证微信消息数据
   */
  private validateWeChatMessage(message: WeChatMessage): boolean {
    // 检查必需字段
    if (!message.id && !message.wechat_message_id) {
      console.warn('[MessageAdapter] 消息ID缺失');
      return false;
    }

    if (!message.message_type && !message.type) {
      console.warn('[MessageAdapter] 消息类型缺失');
      return false;
    }

    // 检查时间戳
    const timestamp = message.metadata?.timestamp || message.timestamp;
    if (!timestamp || timestamp <= 0) {
      console.warn('[MessageAdapter] 消息时间戳无效');
      return false;
    }

    return true;
  }

  /**
   * 提取媒体下载信息
   */
  private extractMediaDownloadInfo(message: WeChatMessage): {
    mediaId: string;
    mediaType: 'image' | 'voice' | 'video' | 'file';
    fileName?: string;
  } | null {
    if (!message.media_id) {
      return null;
    }

    const messageType = message.message_type || message.type || 'text';
    let mediaType: 'image' | 'voice' | 'video' | 'file';

    switch (messageType) {
      case 'image':
        mediaType = 'image';
        break;
      case 'voice':
        mediaType = 'voice';
        break;
      case 'video':
        mediaType = 'video';
        break;
      case 'file':
        mediaType = 'file';
        break;
      default:
        return null;
    }

    return {
      mediaId: message.media_id,
      mediaType,
      fileName: message.metadata?.file_name,
    };
  }

  /**
   * 生成消息内容
   */
  private generateMessageContent(message: WeChatMessage): string {
    const messageType = message.message_type || message.type || 'text';
    const senderName = message.metadata?.from_user || message.sender?.name || 'Unknown';

    switch (messageType) {
      case 'text':
        return message.content || '文本消息';

      case 'image':
        return `[图片] ${senderName}发送了一张图片`;

      case 'voice':
        const duration = message.metadata?.duration || 0;
        return `[语音] ${senderName}发送了一段语音${duration > 0 ? ` (${duration}秒)` : ''}`;

      case 'video':
        return `[视频] ${senderName}发送了一个视频`;

      case 'file':
        const fileName = message.metadata?.file_name || '未知文件';
        const fileSize = message.metadata?.file_size;
        const sizeText = fileSize ? ` (${this.formatFileSize(fileSize)})` : '';
        return `[文件] ${senderName}发送了文件: ${fileName}${sizeText}`;

      case 'event':
        return `[事件] ${message.content || '系统事件'}`;

      default:
        return `[${messageType}] ${message.content || '未知消息类型'}`;
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(message: WeChatMessage): string {
    // 基于发送者和聊天信息生成会话ID
    const senderId = message.metadata?.from_user || message.sender?.id || 'unknown';
    const chatId = message.chatId || 'default';
    const isGroup = message.isGroup || false;

    if (isGroup) {
      return `group_${chatId}`;
    } else {
      return `private_${senderId}`;
    }
  }

  /**
   * 映射消息类型到本地格式（智能类型检测）
   */
  private mapMessageType(wechatType: string, wechatMessage?: WeChatMessage): 'text' | 'image' | 'audio' | 'card' | 'url' | 'file' {
    switch (wechatType) {
      case 'text':
        return 'text';
      case 'image':
        return 'image';
      case 'voice':
        return 'audio';
      case 'video':
        // 视频文件在ChatScreen中作为文件处理，但会显示视频图标
        return 'file';
      case 'file':
        // 根据文件类型智能选择显示方式
        if (wechatMessage) {
          const detectedType = wechatMessage.metadata?.detected_type;
          const mimeType = wechatMessage.mime_type || '';
          const fileName = wechatMessage.metadata?.file_name || '';

          // 图片文件显示为图片
          if (detectedType === 'image' || mimeType.startsWith('image/')) {
            return 'image';
          }

          // 音频文件显示为音频
          if (detectedType === 'voice' || mimeType.startsWith('audio/')) {
            return 'audio';
          }

          // 视频文件显示为文件（但会有视频图标）
          if (detectedType === 'video' || mimeType.startsWith('video/')) {
            return 'file';
          }
        }
        return 'file';
      case 'event':
        return 'card';
      case 'link':
        return 'url';
      default:
        return 'text';
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) {return '0 B';}

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }

  /**
   * 根据文件名获取MIME类型
   */
  private getMimeTypeFromFileName(fileName: string): string {
    if (!fileName) {
      return 'application/octet-stream';
    }

    const extension = fileName.toLowerCase().split('.').pop();

    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'mp3':
        return 'audio/mpeg';
      case 'mp4':
        return 'video/mp4';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * 批量适配微信消息
   */
  batchAdaptWeChatMessages(messages: WeChatMessage[]): {
    success: boolean;
    results: MessageProcessResult[];
    successCount: number;
    failureCount: number;
    mediaDownloadTasks: Array<{
      mediaId: string;
      mediaType: 'image' | 'voice' | 'video' | 'file';
      fileName?: string;
    }>;
  } {
    console.log(`[MessageAdapter] 开始批量适配 ${messages.length} 条微信消息`);

    const results: MessageProcessResult[] = [];
    const mediaDownloadTasks: Array<{
      mediaId: string;
      mediaType: 'image' | 'voice' | 'video' | 'file';
      fileName?: string;
    }> = [];

    let successCount = 0;
    let failureCount = 0;

    for (const message of messages) {
      const result = this.adaptWeChatMessage(message);
      results.push(result);

      if (result.success) {
        successCount++;

        // 收集媒体下载任务
        if (result.needsMediaDownload && result.mediaDownloadInfo) {
          mediaDownloadTasks.push(result.mediaDownloadInfo);
        }
      } else {
        failureCount++;
      }
    }

    console.log(`[MessageAdapter] 批量适配完成: 成功${successCount}条, 失败${failureCount}条, 媒体任务${mediaDownloadTasks.length}个`);

    return {
      success: failureCount === 0,
      results,
      successCount,
      failureCount,
      mediaDownloadTasks,
    };
  }

  /**
   * 更新消息的媒体下载状态
   */
  updateMessageMediaStatus(
    messageExt: MessageExtension,
    status: 'pending' | 'downloading' | 'completed' | 'failed',
    localPath?: string,
    mimeType?: string
  ): MessageExtension {
    const updatedExt = { ...messageExt };

    if (!updatedExt.mediaInfo) {
      updatedExt.mediaInfo = {};
    }

    updatedExt.mediaInfo.downloadStatus = status;

    if (localPath) {
      updatedExt.mediaInfo.localPath = localPath;
    }

    if (mimeType) {
      updatedExt.mediaInfo.mimeType = mimeType;
    }

    return updatedExt;
  }

  /**
   * 解析消息扩展信息
   */
  parseMessageExtension(extString: string): MessageExtension | null {
    try {
      return JSON.parse(extString) as MessageExtension;
    } catch (error) {
      console.error('[MessageAdapter] 解析消息扩展信息失败:', error);
      return null;
    }
  }

  /**
   * 获取消息的媒体下载信息
   */
  getMediaDownloadInfo(localMessage: LocalMessage): {
    mediaId: string;
    mediaType: 'image' | 'voice' | 'video' | 'file';
    fileName?: string;
    downloadStatus?: string;
  } | null {
    if (!localMessage.ext) {
      return null;
    }

    const extension = this.parseMessageExtension(localMessage.ext);
    if (!extension || !extension.mediaId) {
      return null;
    }

    const messageType = extension.messageType;
    let mediaType: 'image' | 'voice' | 'video' | 'file';

    switch (messageType) {
      case 'image':
        mediaType = 'image';
        break;
      case 'voice':
        mediaType = 'voice';
        break;
      case 'video':
        mediaType = 'video';
        break;
      case 'file':
        mediaType = 'file';
        break;
      default:
        return null;
    }

    return {
      mediaId: extension.mediaId,
      mediaType,
      fileName: extension.mediaInfo?.fileName,
      downloadStatus: extension.mediaInfo?.downloadStatus,
    };
  }

  /**
   * 更新消息的媒体URI（下载完成后调用）
   */
  updateMessageMediaUri(messageId: string, mediaType: 'image' | 'voice' | 'video' | 'file', localUri: string): {
    success: boolean;
    updatedExt?: string;
    error?: string;
  } {
    try {
      // 这个方法返回更新后的ext字符串，由调用方负责更新数据库
      const updateData: any = {};

      switch (mediaType) {
        case 'image':
          updateData.mediaUris = [localUri];
          updateData.downloadPending = false;
          break;

        case 'file':
          // 保持原有的fileData结构，只更新uri和下载状态
          const existingFileData = extension.fileData || {};
          updateData.fileData = {
            ...existingFileData,
            uri: localUri,
            downloadPending: false
          };
          break;

        case 'voice':
          updateData.audioData = {
            uri: localUri,
            downloadPending: false
          };
          break;

        default:
          return {
            success: false,
            error: `不支持的媒体类型: ${mediaType}`
          };
      }

      return {
        success: true,
        updatedExt: JSON.stringify(updateData)
      };
    } catch (error) {
      console.error('[MessageAdapter] 更新媒体URI失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }
}

export default new MessageMetadataAdapter();
