/**
 * 微信消息同步服务
 * 基于推送通知和增量同步的消息同步，使用since_id机制
 *
 * 重构版本：
 * - 完全基于since_id的增量同步机制
 * - 集成新的配置管理和服务
 * - 提供生产级别的错误处理和重试机制
 * - 智能同步策略和批量处理
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';
import { addMessage } from '../db/messageService';
import { weChatBindingConfig } from '../config/wechatBindingConfig';
import { SimpleWeChatBindingService } from './SimpleWeChatBindingService';
import WeChatAPIService, { WeChatMessage } from './WeChatAPIService';
import { DeviceRegistrationService } from './DeviceRegistrationService';
import MessageMetadataAdapter from './MessageMetadataAdapter';
import MediaDownloadService from './MediaDownloadService';
import { database } from '../db/index';

// 同步配置接口
interface SyncConfig {
  pushEnabled: boolean;      // 是否启用推送通知
  maxRetries: number;        // 最大重试次数
  batchSize: number;         // 每批次同步的消息数量
  retryDelay: number;        // 重试延迟（毫秒）
  maxConcurrentMedia: number; // 最大并发媒体下载数
  syncTimeout: number;       // 同步超时时间（毫秒）
}

// 同步状态接口
interface SyncStatus {
  lastSyncedId: number;      // 最后同步的消息ID
  lastSyncTime: number;      // 最后同步时间
  messageCount: number;      // 已同步的消息总数
  errorCount: number;        // 同步错误次数
  pushEnabled: boolean;      // 推送通知是否启用
  isRunning: boolean;        // 是否正在同步
  isServiceStarted: boolean; // 服务是否已启动
  lastError?: string;        // 最后一次错误信息
}

// 同步结果接口
interface SyncResult {
  success: boolean;
  messagesProcessed: number;
  newMessageCount: number;
  mediaTaskCount: number;
  error?: string;
  nextSinceId?: number;
}


/**
 * 微信消息同步服务
 */
class WeChatMessageSyncService {
  private appState: AppStateStatus = 'active';
  private appStateSubscription: any;
  private config: SyncConfig;
  private status: SyncStatus;
  private syncLock: boolean = false;
  private retryTimeouts: Map<string, number> = new Map();
  private cleanupTimer: number | null = null;

  constructor() {
    this.initializeConfig();
    this.initializeStatus();

    // 监听应用状态变化
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);

    // 启动定时清理机制（每5分钟清理一次过期的定时器）
    this.startPeriodicCleanup();

    console.log('[WeChatMessageSyncService] 服务已初始化');
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: unknown, defaultMessage: string = '发生未知错误'): string {
    if (error instanceof Error) {
      return error.message;
    }
    return defaultMessage;
  }

  /**
   * 带重试机制的同步
   */
  public async performSyncWithRetry(trigger: string, maxRetries = 3): Promise<SyncResult> {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        if (this.syncLock) {
          console.log(`[WeChatMessageSyncService] 同步锁定中，跳过此次触发: ${trigger}`);
          return { success: false, messagesProcessed: 0, newMessageCount: 0, mediaTaskCount: 0, error: 'Sync locked' };
        }
        this.syncLock = true;
        this.status.isRunning = true;

        await this.performSync();

        this.syncLock = false;
        this.status.isRunning = false;
        // @ts-ignore
        return { success: true, messagesProcessed: this.status.messageCount, newMessageCount: 0, mediaTaskCount: 0 };
      } catch (error: unknown) {
        this.syncLock = false;
        this.status.isRunning = false;
        console.error(`[WeChatMessageSyncService] 同步尝试失败 (第 ${attempt + 1} 次):`, error);
        if (attempt === maxRetries - 1) {
          return { success: false, messagesProcessed: 0, newMessageCount: 0, mediaTaskCount: 0, error: this.formatError(error) };
        }
        await new Promise(res => setTimeout(res, 2000 * (attempt + 1)));
      }
    }
    return { success: false, messagesProcessed: 0, newMessageCount: 0, mediaTaskCount: 0, error: 'Max retries reached' };
  }


  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    const bindingConfig = weChatBindingConfig.getConfig();

    this.config = {
      pushEnabled: true,
      maxRetries: bindingConfig.api.retryAttempts,
      batchSize: parseInt(process.env.WECHAT_SYNC_BATCH_SIZE || '50', 10),
      retryDelay: bindingConfig.api.retryDelay,
      maxConcurrentMedia: parseInt(process.env.WECHAT_SYNC_MAX_CONCURRENT_MEDIA || '3', 10),
      syncTimeout: parseInt(process.env.WECHAT_SYNC_TIMEOUT || '60000', 10), // 60秒
    };

    console.log('[WeChatMessageSyncService] 配置已初始化:', this.config);
  }

  /**
   * 初始化状态
   */
  private initializeStatus(): void {
    this.status = {
      lastSyncedId: 0,
      lastSyncTime: 0,
      messageCount: 0,
      errorCount: 0,
      pushEnabled: true,
      isRunning: false,
      isServiceStarted: false,
    };
  }

  /**
   * 处理应用状态变化
   */
  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    console.log(`[WeChatMessageSyncService] 应用状态变化: ${this.appState} → ${nextAppState}`);

    const previousState = this.appState;
    this.appState = nextAppState;

    // 应用从后台回到前台时，执行一次同步
    if (nextAppState === 'active' && previousState !== 'active') {
      console.log('[WeChatMessageSyncService] 应用回到前台，执行一次同步');
      this.performSyncWithRetry('app_foreground');
    }
  };

  /**
   * 启动消息同步服务
   */
  async startSync(): Promise<boolean> {
    try {
      console.log('[WeChatMessageSyncService] 启动消息同步服务...');

      // 检查服务是否已经启动（防止重复启动）
      if (this.status.isServiceStarted) {
        console.log('[WeChatMessageSyncService] 消息同步服务已启动，跳过重复启动');
        return true;
      }

      // 检查绑定状态
      const canSync = await SimpleWeChatBindingService.getInstance().canSync();
      if (!canSync) {
        console.log('[WeChatMessageSyncService] 未绑定微信，无法启动消息同步');
        return false;
      }

      // 标记服务已启动
      this.status.isServiceStarted = true;
      this.status.pushEnabled = true;

      // 初始化同步状态
      await this.initializeSyncStatus();

      // 立即执行一次同步
      const syncResult = await this.performSyncWithRetry('service_start');

      console.log('[WeChatMessageSyncService] 消息同步服务启动完成:', {
        success: syncResult.success,
        messagesProcessed: syncResult.messagesProcessed,
      });

      return syncResult.success;
    } catch (error) {
      console.error('[WeChatMessageSyncService] 启动同步服务失败:', error);
      this.status.lastError = this.formatError(error);
      this.status.isServiceStarted = false; // 启动失败时重置状态
      return false;
    }
  }

  /**
   * 停止消息同步服务
   */
  stopSync(): void {
    console.log('[WeChatMessageSyncService] 停止消息同步服务');

    this.status.pushEnabled = false;
    this.status.isRunning = false;
    this.status.isServiceStarted = false; // 重置服务启动状态

    // 清除所有重试定时器
    this.clearAllRetryTimeouts();
  }

  /**
   * 检查服务是否已启动
   */
  isServiceStarted(): boolean {
    return this.status.isServiceStarted;
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): SyncStatus {
    return { ...this.status };
  }

  /**
   * 清除所有重试定时器
   */
  private clearAllRetryTimeouts(): void {
    this.retryTimeouts.forEach((timeout, key) => {
      clearTimeout(timeout);
      console.log(`[WeChatMessageSyncService] 清除重试定时器: ${key}`);
    });
    this.retryTimeouts.clear();
  }

  /**
   * 初始化同步状态
   */
  private async initializeSyncStatus(): Promise<void> {
    try {
      // 从设备注册服务获取最后同步的ID
      const lastSyncedId = await DeviceRegistrationService.getLastSyncedId();
      if (lastSyncedId !== null) {
        this.status.lastSyncedId = lastSyncedId;
        console.log('[MessageSync] 恢复最后同步ID:', lastSyncedId);
      }

      // 获取最后同步时间
      const lastSyncTime = await this.getLastSyncTime();
      this.status.lastSyncTime = lastSyncTime;

      console.log('[MessageSync] 同步状态初始化完成:', {
        lastSyncedId: this.status.lastSyncedId,
        lastSyncTime: new Date(this.status.lastSyncTime).toLocaleString(),
      });
    } catch (error) {
      console.error('[MessageSync] 初始化同步状态失败:', error);
    }
  }

  /**
   * 增量同步（使用since_id）
   */
  private async performIncrementalSync(): Promise<void> {
    try {
      console.log('[MessageSync] 开始增量同步，since_id:', this.status.lastSyncedId);

      // 调用新的增量同步API
      const response = await WeChatAPIService.syncMessages({
        sinceId: this.status.lastSyncedId,
        limit: this.config.batchSize,
      });

      if (!response.success || !response.data) {
        console.warn('[MessageSync] 增量同步失败:', response.error);
        this.status.errorCount++;
        return;
      }

      const { messages, has_more, next_since_id } = response.data;

      if (messages.length > 0) {
        await this.saveMessages(messages);
        console.log(`[MessageSync] 增量同步完成，获取 ${messages.length} 条新消息`);

        // 更新同步状态
        this.status.lastSyncedId = next_since_id;
        this.status.messageCount += messages.length;

        // 保存同步状态
        await this.saveSyncStatus();

        // 如果还有更多消息，继续同步
        if (has_more) {
          console.log('[MessageSync] 检测到更多消息，继续同步...');
          await this.performIncrementalSync();
        }
      } else {
        console.log('[MessageSync] 增量同步完成，没有新消息');
      }

      // 更新同步时间
      this.status.lastSyncTime = Date.now();
      await this.saveLastSyncTime(this.status.lastSyncTime);
    } catch (error) {
      console.error('[MessageSync] 增量同步失败:', error);
      this.status.errorCount++;
      throw error;
    }
  }

  /**
   * 执行同步操作
   */
  private async performSync(): Promise<void> {
    try {
      console.log('[MessageSync] 开始同步微信消息...');

      // 检查绑定状态
      const canSync = await SimpleWeChatBindingService.getInstance().canSync();
      if (!canSync) {
        console.log('[MessageSync] 绑定状态已失效，停止同步');
        this.stopSync();
        return;
      }

      // 执行增量同步
      await this.performIncrementalSync();

      // 重置错误计数
      this.status.errorCount = 0;

      console.log('[MessageSync] 同步操作完成');

    } catch (error) {
      console.error('[MessageSync] 同步失败:', error);
      this.status.errorCount++;
    }
  }

  /**
   * 保存同步状态
   */
  private async saveSyncStatus(): Promise<void> {
    try {
      // 更新设备注册服务中的同步状态
      await DeviceRegistrationService.setLastSyncedId(this.status.lastSyncedId);

      // 通知服务端确认同步状态
      await WeChatAPIService.ackSync({
        lastSyncedId: this.status.lastSyncedId,
      });

      console.log('[MessageSync] 同步状态已确认:', this.status.lastSyncedId);
    } catch (error) {
      console.error('[MessageSync] 保存同步状态失败:', error);
    }
  }

  /**
   * 从后端获取新消息（兼容旧接口）
   */
  private async fetchNewMessages(lastSyncTime: number): Promise<WeChatMessage[]> {
    try {
      const newMessages = await WeChatAPIService.fetchNewMessages(lastSyncTime);
      console.log(`[MessageSync] 获取 ${newMessages.length} 条新消息`);
      return newMessages;
    } catch (error) {
      console.error('[MessageSync] 获取新消息失败:', error);
      return [];
    }
  }

  /**
   * 保存消息到数据库
   */
  private async saveMessages(messages: WeChatMessage[]): Promise<void> {
    if (messages.length === 0) {
      console.log('[MessageSync] 没有新消息需要保存');
      return;
    }

    console.log(`[MessageSync] 开始保存 ${messages.length} 条消息到本地数据库`);

    // 使用MessageMetadataAdapter批量适配消息
    const adaptResult = MessageMetadataAdapter.batchAdaptWeChatMessages(messages);

    if (!adaptResult.success) {
      console.warn(`[MessageSync] 消息适配部分失败: 成功${adaptResult.successCount}条, 失败${adaptResult.failureCount}条`);
    }

    let savedCount = 0;
    let errorCount = 0;

    // 保存适配后的消息
    for (const result of adaptResult.results) {
      if (!result.success || !result.localMessage) {
        console.warn(`[MessageSync] 跳过适配失败的消息: ${result.error}`);
        errorCount++;
        continue;
      }

      try {
        // 保存到数据库
        await addMessage(result.localMessage);
        savedCount++;

        console.log(`[MessageSync] 已保存消息 ${result.localMessage.ext ? JSON.parse(result.localMessage.ext).wechatMessageId : 'unknown'} (${savedCount}/${adaptResult.successCount})`);
      } catch (error: unknown) {
        errorCount++;
        console.error('[MessageSync] 保存消息失败:', error);

        // 如果是重复消息错误，不算作错误
        if (error instanceof Error && error.message?.includes('UNIQUE constraint failed')) {
          console.log('[MessageSync] 消息已存在，跳过');
          errorCount--; // 不计入错误数
        }
      }
    }

    // 处理媒体下载任务
    if (adaptResult.mediaDownloadTasks.length > 0) {
      console.log(`[MessageSync] 开始处理 ${adaptResult.mediaDownloadTasks.length} 个媒体下载任务`);

      // 异步处理媒体下载，不阻塞消息保存
      this.processMediaDownloadTasks(adaptResult.mediaDownloadTasks).catch(error => {
        console.error('[MessageSync] 媒体下载任务处理失败:', error);
      });
    }

    console.log(`[MessageSync] 消息保存完成: 成功${savedCount}条, 错误${errorCount}条, 媒体任务${adaptResult.mediaDownloadTasks.length}个`);
  }

  /**
   * 处理媒体下载任务
   */
  private async processMediaDownloadTasks(tasks: Array<{
    mediaId: string;
    mediaType: 'image' | 'voice' | 'video' | 'file';
    fileName?: string;
  }>): Promise<void> {
    try {
      console.log(`[MessageSync] 开始批量下载 ${tasks.length} 个媒体文件`);

      // 批量下载媒体文件
      const downloadResults = await MediaDownloadService.batchDownload(
        tasks,
        {
          onProgress: (completed, total) => {
            console.log(`[MessageSync] 媒体下载进度: ${completed}/${total}`);
          },
          onItemComplete: async (mediaId, result) => {
            if (result.success && result.filePath) {
              console.log(`[MessageSync] 媒体文件下载成功: ${mediaId}`);

              // 查找对应的任务信息
              const task = tasks.find(t => t.mediaId === mediaId);
              if (task) {
                // 更新消息的媒体URI
                await this.updateMessageMediaUri(mediaId, task.mediaType, result.filePath);
              }
            } else {
              console.warn(`[MessageSync] 媒体文件下载失败: ${mediaId}, ${result.error}`);
            }
          },
        }
      );

      const successCount = downloadResults.filter(r => r.success).length;
      console.log(`[MessageSync] 媒体下载完成: ${successCount}/${tasks.length} 成功`);

    } catch (error) {
      console.error('[MessageSync] 媒体下载任务处理异常:', error);
    }
  }

  /**
   * 更新消息的媒体URI
   */
  private async updateMessageMediaUri(mediaId: string, mediaType: 'image' | 'voice' | 'video' | 'file', localUri: string): Promise<void> {
    try {
      console.log(`[MessageSync] 更新消息媒体URI: ${mediaId} -> ${localUri}`);

      // 查找包含该mediaId的消息
      const collection = database.get('messages');
      const messages = await collection.query().fetch();

      for (const message of messages) {
        if (message.ext) {
          try {
            const ext = JSON.parse(message.ext);
            if (ext.mediaId === mediaId) {
              console.log(`[MessageSync] 找到匹配的消息: ${message.id}`);

              // 更新ext字段
              const updatedExt = { ...ext };

              switch (mediaType) {
                case 'image':
                  updatedExt.mediaUris = [localUri];
                  updatedExt.downloadPending = false;
                  break;

                case 'file':
                  if (!updatedExt.fileData) updatedExt.fileData = {};
                  updatedExt.fileData.uri = localUri;
                  updatedExt.fileData.downloadPending = false;
                  break;

                case 'voice':
                  if (!updatedExt.audioData) updatedExt.audioData = {};
                  updatedExt.audioData.uri = localUri;
                  updatedExt.audioData.downloadPending = false;
                  break;
              }

              // 更新数据库
              await database.write(async () => {
                await message.update(msg => {
                  msg.ext = JSON.stringify(updatedExt);
                });
              });

              console.log(`[MessageSync] 消息媒体URI更新成功: ${message.id}`);
              break; // 找到并更新后退出循环
            }
          } catch (parseError) {
            // 忽略JSON解析错误
          }
        }
      }
    } catch (error) {
      console.error(`[MessageSync] 更新消息媒体URI失败: ${mediaId}`, error);
    }
  }

  /**
   * 获取上次同步时间
   */
  private async getLastSyncTime(): Promise<number> {
    try {
      const timeStr = await AsyncStorage.getItem('wechat_last_sync_time');
      return timeStr ? parseInt(timeStr, 10) : Date.now() - 24 * 60 * 60 * 1000; // 默认24小时前
    } catch (error) {
      console.error('[MessageSync] 获取上次同步时间失败:', error);
      return Date.now() - 24 * 60 * 60 * 1000;
    }
  }

  /**
   * 保存上次同步时间
   */
  private async saveLastSyncTime(timestamp: number): Promise<void> {
    try {
      await AsyncStorage.setItem('wechat_last_sync_time', timestamp.toString());
    } catch (error) {
      console.error('[MessageSync] 保存同步时间失败:', error);
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): SyncStatus {
    return { ...this.status };
  }

  /**
   * 手动触发一次同步
   */
  async manualSync(): Promise<{ success: boolean; message: string }> {
    try {
      await this.performSync();
      return {
        success: true,
        message: '手动同步完成',
      };
    } catch (error) {
      return {
        success: false,
        message: `同步失败: ${(error as Error).message}`,
      };
    }
  }

  /**
   * 重置同步状态（用于重新开始同步）
   */
  async resetSyncStatus(): Promise<void> {
    try {
      this.status.lastSyncedId = 0;
      this.status.lastSyncTime = 0;
      this.status.messageCount = 0;
      this.status.errorCount = 0;

      await DeviceRegistrationService.setLastSyncedId(0);
      await AsyncStorage.removeItem('wechat_last_sync_time');

      console.log('[MessageSync] 同步状态已重置');
    } catch (error) {
      console.error('[MessageSync] 重置同步状态失败:', error);
    }
  }

  /**
   * 启动定时清理机制
   */
  private startPeriodicCleanup(): void {
    // 每5分钟清理一次
    this.cleanupTimer = setInterval(() => {
      this.performPeriodicCleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * 执行定时清理
   */
  private performPeriodicCleanup(): void {
    const beforeCount = this.retryTimeouts.size;

    // 清理无效的定时器（如果定时器数量过多，进行清理）
    if (beforeCount > 10) {
      console.log(`[WeChatMessageSyncService] 执行定时清理，当前定时器数量: ${beforeCount}`);
      this.clearAllRetryTimeouts();
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    console.log('[WeChatMessageSyncService] 清理资源');

    this.stopSync();

    // 清理定时清理器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 强制清理所有重试定时器
    this.clearAllRetryTimeouts();

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    console.log('[WeChatMessageSyncService] 资源清理完成');
  }

  /**
   * 验证微信消息数据
   */
  private validateMessageData(message: WeChatMessage): boolean {
    try {
      // 基本字段验证
      if (!message.id || (typeof message.id !== 'string' && typeof message.id !== 'number')) {
        console.warn('[MessageSync] 消息ID无效:', message.id);
        return false;
      }

      // 消息类型验证
      if (!message.message_type && !message.type) {
        console.warn('[MessageSync] 消息类型缺失:', message);
        return false;
      }

      // 元数据验证
      if (!message.metadata && !message.timestamp) {
        console.warn('[MessageSync] 消息元数据缺失:', message);
        return false;
      }

      return true;
    } catch (error) {
      console.error('[MessageSync] 消息验证异常:', error);
      return false;
    }
  }

  /**
   * 清理消息内容（防止XSS等安全问题）
   */
  private sanitizeContent(content: string): string {
    if (!content || typeof content !== 'string') {
      return '';
    }

    // 移除潜在的危险字符和脚本
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // 移除iframe标签
      .replace(/javascript:/gi, '') // 移除javascript协议
      .replace(/on\w+\s*=/gi, '') // 移除事件处理器
      .trim()
      .substring(0, 5000); // 限制长度
  }

  /**
   * 将微信消息类型映射到本地消息类型
   */
  private mapWeChatMessageType(wechatType: string): 'text' | 'image' | 'audio' | 'card' | 'url' | 'file' {
    switch (wechatType) {
      case 'text':
        return 'text';
      case 'image':
        return 'image';
      case 'voice':
        return 'audio';
      case 'video':
        return 'file'; // 视频作为文件处理
      case 'file':
        return 'file';
      case 'event':
        return 'card'; // 事件消息作为卡片处理
      default:
        return 'text'; // 默认文本类型
    }
  }
}

export default new WeChatMessageSyncService();
