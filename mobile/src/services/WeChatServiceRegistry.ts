/**
 * 微信服务注册表
 * 统一管理所有微信相关服务，避免重复导入和服务冲突
 *
 * 重构说明：
 * 1. 统一使用 Simple 系列服务（功能更完整，使用更广泛）
 * 2. 提供统一的服务访问接口
 * 3. 简化服务间的依赖关系
 */

// 核心服务导入 - 导入实例和类型
import simpleWeChatBindingService, { SimpleWeChatBindingService } from './SimpleWeChatBindingService';
import weChatMessageSyncService from './WeChatMessageSyncService';
import weChatAPIService from './WeChatAPIService';
import jPushService from './JPushService';
import mediaDownloadService from './MediaDownloadService';
import { DeviceRegistrationService } from './DeviceRegistrationService';
import accessTokenManager from './AccessTokenManager';
import { SignatureService } from './SignatureService';

// 导入类型定义
type WeChatMessageSyncServiceType = typeof weChatMessageSyncService;
type WeChatAPIServiceType = typeof weChatAPIService;
type JPushServiceType = typeof jPushService;
type MediaDownloadServiceType = typeof mediaDownloadService;
type DeviceRegistrationServiceType = typeof DeviceRegistrationService;
type AccessTokenManagerType = typeof accessTokenManager;

// 服务实例类型定义
export interface WeChatServices {
  bindingService: SimpleWeChatBindingService;
  syncService: WeChatMessageSyncServiceType;
  apiService: WeChatAPIServiceType;
  pushService: JPushServiceType;
  mediaService: MediaDownloadServiceType;
  deviceService: DeviceRegistrationServiceType;
  tokenManager: AccessTokenManagerType;
  signatureService: typeof SignatureService;
}

/**
 * 微信服务注册表
 * 单例模式管理所有微信相关服务
 */
class WeChatServiceRegistry {
  private static instance: WeChatServiceRegistry;
  private services: WeChatServices;
  private initialized = false;

  private constructor() {
    this.services = this.initializeServices();
  }

  /**
   * 获取服务注册表实例
   */
  public static getInstance(): WeChatServiceRegistry {
    if (!WeChatServiceRegistry.instance) {
      WeChatServiceRegistry.instance = new WeChatServiceRegistry();
    }
    return WeChatServiceRegistry.instance;
  }

  /**
   * 初始化所有服务
   */
  private initializeServices(): WeChatServices {
    console.log('[WeChatServiceRegistry] 开始初始化微信服务...');

    // 使用导入的服务实例
    const bindingService = simpleWeChatBindingService;
    const syncService = weChatMessageSyncService;
    const apiService = weChatAPIService;
    const pushService = jPushService;
    const mediaService = mediaDownloadService;
    const deviceService = DeviceRegistrationService;
    const tokenManager = accessTokenManager;
    const signatureService = SignatureService;

    // 设置服务间依赖关系
    this.setupServiceDependencies({
      bindingService,
      syncService,
      apiService,
      pushService,
      mediaService,
      deviceService,
      tokenManager,
      signatureService,
    });

    console.log('[WeChatServiceRegistry] 微信服务初始化完成');

    return {
      bindingService,
      syncService,
      apiService,
      pushService,
      mediaService,
      deviceService,
      tokenManager,
      signatureService,
    };
  }

  /**
   * 设置服务间依赖关系
   */
  private setupServiceDependencies(services: WeChatServices): void {
    try {
      // 注意：不在这里初始化JPush，避免重复初始化
      // JPush的初始化由App.tsx统一管理

      // 如果JPush已经初始化，设置注册ID到绑定服务
      if (services.pushService.isReady()) {
        const registrationId = services.pushService.getRegistrationID();
        if (registrationId) {
          services.bindingService.setJPushRegistrationId(registrationId);
        }
      }

      console.log('[WeChatServiceRegistry] 服务依赖关系设置完成');
    } catch (error) {
      console.error('[WeChatServiceRegistry] 设置服务依赖关系失败:', error);
    }
  }

  /**
   * 获取所有服务
   */
  public getServices(): WeChatServices {
    return this.services;
  }

  /**
   * 获取绑定服务
   */
  public getBindingService(): SimpleWeChatBindingService {
    return this.services.bindingService;
  }

  /**
   * 获取同步服务
   */
  public getSyncService(): WeChatMessageSyncServiceType {
    return this.services.syncService;
  }

  /**
   * 获取API服务
   */
  public getAPIService(): WeChatAPIServiceType {
    return this.services.apiService;
  }

  /**
   * 获取推送服务
   */
  public getPushService(): JPushServiceType {
    return this.services.pushService;
  }

  /**
   * 获取媒体下载服务
   */
  public getMediaService(): MediaDownloadServiceType {
    return this.services.mediaService;
  }

  /**
   * 获取设备注册服务
   */
  public getDeviceService(): DeviceRegistrationServiceType {
    return this.services.deviceService;
  }

  /**
   * 获取Token管理器
   */
  public getTokenManager(): AccessTokenManagerType {
    return this.services.tokenManager;
  }

  /**
   * 获取签名服务
   */
  public getSignatureService(): SignatureService {
    return this.services.signatureService;
  }

  /**
   * 初始化所有服务
   */
  public async initialize(): Promise<boolean> {
    if (this.initialized) {
      return true;
    }

    try {
      console.log('[WeChatServiceRegistry] 开始初始化微信服务...');

      // 初始化推送服务
      await this.services.pushService.initialize();

      // 初始化设备注册服务
      await this.services.deviceService.getInstance().initializeDeviceRegistration();

      // 初始化同步服务
      await this.services.syncService.startSync();

      this.initialized = true;
      console.log('[WeChatServiceRegistry] 微信服务初始化完成');
      return true;
    } catch (error) {
      console.error('[WeChatServiceRegistry] 微信服务初始化失败:', error);
      return false;
    }
  }

  /**
   * 销毁所有服务
   */
  public async destroy(): Promise<void> {
    try {
      console.log('[WeChatServiceRegistry] 开始销毁微信服务...');

      // 销毁同步服务
      if (this.services.syncService && typeof this.services.syncService.destroy === 'function') {
        this.services.syncService.destroy();
      }

      // 推送服务没有destroy方法，只需要停止监听
      // JPushService 没有提供destroy方法

      this.initialized = false;
      console.log('[WeChatServiceRegistry] 微信服务销毁完成');
    } catch (error) {
      console.error('[WeChatServiceRegistry] 微信服务销毁失败:', error);
    }
  }

  /**
   * 获取服务状态
   */
  public getServiceStatus(): {
    initialized: boolean;
    services: {
      [key: string]: boolean;
    };
  } {
    return {
      initialized: this.initialized,
      services: {
        bindingService: !!this.services.bindingService,
        syncService: !!this.services.syncService,
        apiService: !!this.services.apiService,
        pushService: !!this.services.pushService,
        mediaService: !!this.services.mediaService,
        deviceService: !!this.services.deviceService,
        tokenManager: !!this.services.tokenManager,
        signatureService: !!this.services.signatureService,
      },
    };
  }
}

// 导出单例实例
export const weChatServiceRegistry = WeChatServiceRegistry.getInstance();

// 导出便捷访问方法
export const getWeChatServices = () => weChatServiceRegistry.getServices();
export const getWeChatBindingService = () => weChatServiceRegistry.getBindingService();
export const getWeChatSyncService = () => weChatServiceRegistry.getSyncService();
export const getWeChatAPIService = () => weChatServiceRegistry.getAPIService();
export const getWeChatPushService = () => weChatServiceRegistry.getPushService();
export const getWeChatMediaService = () => weChatServiceRegistry.getMediaService();

// 默认导出
export default WeChatServiceRegistry;
