// 聊天相关工具函数

// 复制自 ChatScreen.tsx 的 Message 类型定义
export interface Message {
  id: string;
  text: string;
  createdAt: Date;
  sender: 'user' | 'assistant';
  type: 'text' | 'image' | 'url' | 'card' | 'audio' | 'file';
  status?: 'sending' | 'sent' | 'failed' | 'received';
  mediaUris?: string[];
  audioData?: {
    uri: string;
    duration: number;
  };
  cardData?: {
    title: string;
    items: string[];
    action: {
      label: string;
      done: boolean;
    };
  };
  urlData?: {
    url: string;
    title: string;
    description: string;
    image: string;
  };
  fileData?: {
    uri: string;
    name: string;
    size: number;
    type: string;
    originalUri?: string;
  };
  participants: string;
}

export function formatTimestamp(date: Date): string {
  const now = new Date();
  const isToday = date.toDateString() === now.toDateString();
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const isYesterday = date.toDateString() === yesterday.toDateString();
  const pad = (n: number) => n.toString().padStart(2, '0');
  if (isToday) {
    return `${pad(date.getHours())}:${pad(date.getMinutes())}`;
  } else if (isYesterday) {
    return `昨天 ${pad(date.getHours())}:${pad(date.getMinutes())}`;
  } else if (date.getFullYear() === now.getFullYear()) {
    return `${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
  } else {
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
  }
}

export function shouldShowTimestamp(index: number, msgList: Message[]): boolean {
  if (index === msgList.length - 1) {return true;} // 最早一条消息（底部）始终显示
  const curr = msgList[index];
  const next = msgList[index + 1];
  return (curr.createdAt.getTime() - next.createdAt.getTime()) > 5 * 60 * 1000;
}
