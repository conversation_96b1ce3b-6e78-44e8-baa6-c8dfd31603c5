// 文件类型元数据映射表及工具函数

export interface FileTypeMeta {
  key: string;
  description: string;
  icon: string;
  mimeTypes: string[];
  extensions: string[];
}

// 文件类型元数据列表（可扩展）
export const fileTypeMetaList: FileTypeMeta[] = [
  {
    key: 'pdf',
    description: 'PDF',
    icon: 'document-text',
    mimeTypes: ['application/pdf'],
    extensions: ['.pdf'],
  },
  {
    key: 'ofd',
    description: 'OFD',
    icon: 'shield-checkmark',
    mimeTypes: ['application/ofd', 'application/vnd.ofd'],
    extensions: ['.ofd'],
  },
  {
    key: 'word',
    description: 'Word',
    icon: 'document-text-outline',
    mimeTypes: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-word.document.macroEnabled.12',
    ],
    extensions: ['.doc', '.docx', '.docm', '.wps'],
  },
  {
    key: 'excel',
    description: 'Excel',
    icon: 'grid-outline',
    mimeTypes: [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
    ],
    extensions: ['.xls', '.xlsx', '.xlsm', '.et'],
  },
  {
    key: 'powerpoint',
    description: 'PowerPoint',
    icon: 'easel-outline',
    mimeTypes: [
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    ],
    extensions: ['.ppt', '.pptx', '.pptm', '.dps'],
  },
  {
    key: 'wps-writer',
    description: 'WPS文字',
    icon: 'document-text-outline',
    mimeTypes: ['application/wps-office.wps'],
    extensions: ['.wps'],
  },
  {
    key: 'wps-spreadsheet',
    description: 'WPS表格',
    icon: 'grid-outline',
    mimeTypes: ['application/wps-office.et'],
    extensions: ['.et'],
  },
  {
    key: 'wps-presentation',
    description: 'WPS演示',
    icon: 'easel-outline',
    mimeTypes: ['application/wps-office.dps'],
    extensions: ['.dps'],
  },
  {
    key: 'text',
    description: '纯文本',
    icon: 'document-outline',
    mimeTypes: ['text/plain'],
    extensions: ['.txt'],
  },
  {
    key: 'rtf',
    description: '富文本',
    icon: 'document-outline',
    mimeTypes: ['application/rtf', 'text/rtf'],
    extensions: ['.rtf'],
  },
  {
    key: 'csv',
    description: 'CSV数据',
    icon: 'stats-chart-outline',
    mimeTypes: ['text/csv'],
    extensions: ['.csv'],
  },
  // 其他类型
  {
    key: 'zip',
    description: '压缩文件',
    icon: 'archive-outline',
    mimeTypes: [],
    extensions: ['.zip', '.rar', '.7z'],
  },
  {
    key: 'json',
    description: 'JSON数据',
    icon: 'code-outline',
    mimeTypes: ['application/json'],
    extensions: ['.json'],
  },
  {
    key: 'md',
    description: 'Markdown文档',
    icon: 'reader-outline',
    mimeTypes: ['text/markdown'],
    extensions: ['.md'],
  },
  {
    key: 'xml',
    description: 'XML文档',
    icon: 'code-outline',
    mimeTypes: ['application/xml', 'text/xml'],
    extensions: ['.xml'],
  },
  {
    key: 'image',
    description: '图片文件',
    icon: 'image-outline',
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'],
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
  },
  {
    key: 'video',
    description: '视频文件',
    icon: 'videocam-outline',
    mimeTypes: ['video/mp4', 'video/avi', 'video/mpeg', 'video/quicktime'],
    extensions: ['.mp4', '.avi', '.mpeg', '.mov'],
  },
  {
    key: 'audio',
    description: '音频文件',
    icon: 'musical-notes-outline',
    mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/aac', 'audio/ogg'],
    extensions: ['.mp3', '.wav', '.aac', '.ogg'],
  },
];

// 获取所有支持的MIME类型
export function getAllSupportedMimeTypes(): string[] {
  return fileTypeMetaList.flatMap(meta => meta.mimeTypes).filter(Boolean);
}

// 获取所有支持的扩展名
export function getAllSupportedExtensions(): string[] {
  return fileTypeMetaList.flatMap(meta => meta.extensions).filter(Boolean);
}

// 根据mimeType和文件名获取文件类型元数据
export function getFileTypeMeta(mimeType: string, fileName: string): FileTypeMeta | undefined {
  const ext = fileName.toLowerCase().split('.').pop();
  // 先按mimeType匹配
  let found = fileTypeMetaList.find(meta => meta.mimeTypes.some(m => mimeType.toLowerCase().includes(m.toLowerCase())));
  if (found) {return found;}
  // 再按扩展名匹配
  if (ext) {
    found = fileTypeMetaList.find(meta => meta.extensions.includes('.' + ext));
    if (found) {return found;}
  }
  return undefined;
}

// 获取icon
export function getFileIcon(mimeType: string, fileName: string): string {
  const meta = getFileTypeMeta(mimeType, fileName);
  return meta ? meta.icon : 'document-outline';
}

// 获取描述
export function getFileTypeDescription(mimeType: string, fileName: string): string {
  const meta = getFileTypeMeta(mimeType, fileName);
  if (meta) {return meta.description;}
  // fallback
  const ext = fileName.toLowerCase().split('.').pop();
  return ext ? `${ext.toUpperCase()}文件` : '文档';
}

// 判断是否支持该类型
export function isSupportedFileType(mimeType: string, fileName: string): boolean {
  return !!getFileTypeMeta(mimeType, fileName);
}
